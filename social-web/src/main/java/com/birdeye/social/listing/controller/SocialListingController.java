package com.birdeye.social.listing.controller;

import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.dto.GmbPageAnalyticsEvent;
import com.birdeye.social.dto.GoogleLocationSearchResponse;
import com.birdeye.social.external.request.facebook.FbProfileInfoRequest;
import com.birdeye.social.external.request.google.FoodMenusDTO;
import com.birdeye.social.external.request.google.GMBMedia;
import com.birdeye.social.external.response.google.GMBPageLocation;
import com.birdeye.social.external.response.google.GMBProfileLocation;
import com.birdeye.social.model.FbPublicProfileInfo;
import com.birdeye.social.service.CommonService;
import com.birdeye.social.service.FacebookPageService;
import com.birdeye.social.service.SocialReportService.GMB.GmbReportService;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import com.birdeye.social.external.request.google.GMBAnswerDTO;
import com.birdeye.social.external.request.google.GMBQuestionDTO.QuestionDTO;
import com.birdeye.social.external.response.google.GMBLocationUpdateResponse;
import com.birdeye.social.external.response.google.GMBPageLocationForCore;
import com.birdeye.social.model.LocationInfoMessage;
import com.birdeye.social.service.GMBLocationDetailService;
import com.birdeye.social.service.SocialPostService;
import com.birdeye.social.service.SocialReportService.SocialReportService;

/**
 * <AUTHOR> Luthra
 * 
 * Separate Controller for Listing APIs.
 *
 */

@RestController
@RequestMapping("/")
public class SocialListingController {
	
	@Autowired
	private GMBLocationDetailService gmbLocationService;
	
	@Autowired
	private SocialPostService socialPostService;
	
	@Autowired
	private SocialReportService socialReportService;

	@Autowired
	private FacebookPageService facebookPageService;

	@Autowired
	private GmbReportService gmbReportService;

	@Autowired
	private CommonService commonService;

//	private static final Logger log = LoggerFactory.getLogger(SocialListingController.class);
	
	/**
	 * API to get the details of a GMB location.
	 * Refer - https://developers.google.com/my-business/reference/rest/v4/accounts.locations/get
	 *
	 * @param businessId
	 * @return GMBPageLocation
	 * @throws Exception
	 */
	@GetMapping("social/gmb/business/{businessId}")
	public ResponseEntity<GMBPageLocationForCore> getGMBPageInfoForBusiness(@PathVariable("businessId") Integer businessId,
																			@RequestParam("supportedAttributes") Boolean supportedAttributes,
																			@RequestHeader(value = "rate-limit-identifier", required = false,
																					defaultValue = "LISTING") String rateLimitIdentifier) throws Exception {
		validateRateLimit(rateLimitIdentifier);
		return new ResponseEntity<>(gmbLocationService.getGMBPageInfoForBusiness(businessId, supportedAttributes), HttpStatus.OK);
	}
	
	/**
	 * API to update the information present on social pages of GMB & FB
	 * Called by BE Dashboard Profile page to sync BE & Social Pages
	 * @param businessId
	 * @param channel
	 * @param locationInfoMessage
	 * @param update
	 * @return
	 * @throws Exception
	 */
	
	@PostMapping(value = "social/post/{channel}/update/attributes")
	public @ResponseBody ResponseEntity<?> updateGMBAttributes(@RequestParam("businessId") Integer businessId,
			@PathVariable("channel") String channel,@RequestBody LocationInfoMessage locationInfoMessage,@RequestParam("update") boolean update)
			throws Exception {
		return new ResponseEntity<>(socialPostService.updateGMBAttributes(channel,businessId,locationInfoMessage,update), HttpStatus.OK);
	}
	
	/**
	 * API to get Google Update status from diffMask
	 * 
	 * @param businessId
	 * @param rateLimitIdentifier
	 * @return
	 * @throws Exception
	 */
	@GetMapping("social/gmb/business/{businessId}/update-mask")
	public ResponseEntity<GMBLocationUpdateResponse> getGMBPageUpdateInfoForBusiness(
			@PathVariable("businessId") Integer businessId,@RequestParam(name = "readMask", required = false, defaultValue = "name,metadata")String readMask,

			@RequestHeader(value = "rate-limit-identifier", required = false, defaultValue = "LISTING") String rateLimitIdentifier)
			throws Exception {
		validateRateLimit(rateLimitIdentifier);
		return new ResponseEntity<>(gmbLocationService.getGMBPageUpdateInfoForBusiness(businessId, readMask), HttpStatus.OK);
	}
    
    private void validateRateLimit(String rateLimitIdentifier) {
		Boolean rateLimitingEnabled = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getRateLimitingCheckEnabled();
		if(Boolean.TRUE.equals(rateLimitingEnabled)) {
			MDC.put("source",rateLimitIdentifier);
			MDC.put("channel", "GMB");
		}
	}
	

    /**
     * persist GMB Insights to ES
     * @param channel
     * @return
     * @throws Exception
     */
    @PutMapping("social/reports/{channel}/page/analytics")
	public ResponseEntity<Void> getGMBPageAnalytics(
			@PathVariable("channel") String channel,
			@RequestParam(name = "businessId", required = true) Integer businessId,
			@RequestParam(name = "pageId", required = true) String pageId) throws Exception {
        socialReportService.getGMBPageAnalytics(businessId,pageId,channel);
        return new ResponseEntity<>(HttpStatus.OK);
    }

	@PutMapping("social/reports/gmb/fetch/analytics")
	public ResponseEntity<Void> fetchAndSaveGmbAnalytics(@RequestBody GmbPageAnalyticsEvent pageAnalyticsEvent) {
		gmbReportService.fetchAndSaveGmbPageAnalytics(pageAnalyticsEvent);
		return new ResponseEntity<>(HttpStatus.OK);
	}

    @PutMapping("social/reports/{channel}/keyword/analytics")
    public ResponseEntity<Void> getGMBKeyWordAnalytics(
            @PathVariable("channel") String channel,
            @RequestParam(name = "businessId", required = true) Integer businessId) throws Exception {
        socialReportService.getGMBKeywordAnalytics(businessId, channel);
        return new ResponseEntity<>(HttpStatus.OK);
    }
	
	@PostMapping(value = "social/post/create/question")
	public @ResponseBody ResponseEntity<?> createGMBQuestion(@RequestParam("businessId") Integer businessId,
			@RequestBody QuestionDTO question) throws Exception {
		return new ResponseEntity<>(socialPostService.createGMBQuestion(businessId, question), HttpStatus.OK);
	}

	@PostMapping(value = "social/post/upsert/answer")
	public @ResponseBody ResponseEntity<?> upsertGmbAnswer(@RequestParam("businessId") Integer businessId,
			@RequestBody GMBAnswerDTO answer) throws Exception {
		return new ResponseEntity<>(socialPostService.upsertGmbAnswer(businessId, answer), HttpStatus.OK);
	}

	@PostMapping(value = "social/post/update/question")
	public @ResponseBody ResponseEntity<?> updateGMBQuestion(@RequestParam("businessId") Integer businessId,
			@RequestBody QuestionDTO question) throws Exception {
		return new ResponseEntity<>(socialPostService.updateGMBQuestion(businessId, question), HttpStatus.OK);
	}

	@GetMapping(value = "social/post/return/questionsList/{businessId}")
	public @ResponseBody ResponseEntity<?> returnGMBQuestionsList(@PathVariable("businessId") Integer businessId)throws Exception {
		return new ResponseEntity<>(socialPostService.returnGMBQuestionsList(businessId, true), HttpStatus.OK);
	}

	@PostMapping(value = "social/post/delete/question")
	public @ResponseBody ResponseEntity<?> deleteGmbQuestion(@RequestParam("businessId") Integer businessId,
			@RequestBody QuestionDTO question) throws Exception {
		return new ResponseEntity<>(socialPostService.deleteGmbQuestion(businessId, question), HttpStatus.OK);
	}

	@PostMapping(value = "social/post/return/answersList/{businessId}")
	public @ResponseBody ResponseEntity<?> returnGMBAnswersList(@PathVariable("businessId") Integer businessId,
			@RequestBody GMBAnswerDTO answer)throws Exception {
		return new ResponseEntity<>(socialPostService.returnGMBAnswersList(businessId, answer), HttpStatus.OK);
	}

	@PostMapping(value = "social/post/delete/answer")
	public @ResponseBody ResponseEntity<?> deleteGmbAnswer(@RequestParam("businessId") Integer businessId,
			@RequestBody GMBAnswerDTO answer) throws Exception {
		return new ResponseEntity<>(socialPostService.deleteGmbAnswer(businessId, answer), HttpStatus.OK);
	}

	/**
	 *
	 * @param fbProfileInfoRequest
	 * @return
	 * @throws Exception
	 * This API should be used only to get profile data
	 * Called via Kafka: fb-profile-info
	 */
	@PostMapping(value = "social/facebook/profile/info")
	public @ResponseBody ResponseEntity<FbPublicProfileInfo> facebookProfileInfo(@RequestBody FbProfileInfoRequest fbProfileInfoRequest) throws Exception {
		return new ResponseEntity<>(facebookPageService.facebookProfileInfo(fbProfileInfoRequest), HttpStatus.OK);
	}

	@GetMapping(value = "social/gmb/{businessId}/food-menus")
	public @ResponseBody ResponseEntity<FoodMenusDTO> getGmbFoodMenus(@PathVariable("businessId") Integer businessId,
																	  @RequestHeader(value = "rate-limit-identifier", required = false, defaultValue = "LISTING") String rateLimitIdentifier) throws Exception {
		validateRateLimit(rateLimitIdentifier);
		return new ResponseEntity<>(gmbLocationService.getGmbFoodMenus(businessId), HttpStatus.OK);
	}

	@PatchMapping(value = "social/gmb/food-menus")
	public ResponseEntity<?> updateGmbFoodMenus(@RequestBody FoodMenusDTO foodMenus,
												@RequestHeader(value = "rate-limit-identifier", required = false, defaultValue = "LISTING") String rateLimitIdentifier) {
		validateRateLimit(rateLimitIdentifier);
		gmbLocationService.updateGmbFoodMenus(foodMenus);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@GetMapping(value = "social/gmb/{businessId}/media")
	public @ResponseBody ResponseEntity<GMBMedia> getGMBMedia(@PathVariable("businessId") Integer businessId,
															  @RequestHeader(value = "rate-limit-identifier", required = false, defaultValue = "LISTING") String rateLimitIdentifier) throws Exception {
		validateRateLimit(rateLimitIdentifier);
		return new ResponseEntity<>(gmbLocationService.getGMBMedia(businessId), HttpStatus.OK);
	}

	@PatchMapping(value = "social/gmb/location-profile/{businessId}")
	public ResponseEntity<GMBProfileLocation> updateGMBLocationProfile(@PathVariable("businessId") Integer businessId, @RequestBody GMBProfileLocation location,
																	   @RequestParam (name = "updateMask") String updateMask,
																	   @RequestHeader(value = "rate-limit-identifier", required = false, defaultValue = "LISTING") String rateLimitIdentifier) {
		validateRateLimit(rateLimitIdentifier);
		GMBProfileLocation updatedLocation = gmbLocationService.updateGmbLocationProfile(location, businessId, updateMask);
		return new ResponseEntity<>(updatedLocation, HttpStatus.OK);
	}
}
