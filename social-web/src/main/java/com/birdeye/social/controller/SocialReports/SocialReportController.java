package com.birdeye.social.controller.SocialReports;

import com.birdeye.social.dto.CDNMigrationAccount;
import com.birdeye.social.dto.report.PostInsightsEventRequest;
import com.birdeye.social.dto.report.SocialScanEventDTO;
import com.birdeye.social.entities.BusinessFBPage;
import com.birdeye.social.facebook.NewFbPostData;
import com.birdeye.social.insights.CDNEventRequest;
import com.birdeye.social.insights.SocialInsightsAuditDto;
import com.birdeye.social.insights.PageInsights;
import com.birdeye.social.model.ChannelPageRemoved;
import com.birdeye.social.model.IgBackFillInsightReq;
import com.birdeye.social.model.IgStoryEventRequest;
import com.birdeye.social.model.InstagramEventRequest;
import com.birdeye.social.service.CommonService;
import com.birdeye.social.service.SocialReportService.SocialReportService;
import com.birdeye.social.sro.LocationPageMappingRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

@RestController
@RequestMapping("social/reports")
public class SocialReportController {

    @Autowired
    SocialReportService socialReportService;

    @Autowired
    CommonService commonService;

    @PostMapping("/{channel}/page-insights")
    public ResponseEntity<?> postPageInsights(@RequestBody PageInsights pageInsights, @PathVariable("channel") String channel) {
        socialReportService.postPageInsights(pageInsights, channel);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping("/post-insights")
    public ResponseEntity<?> getPostInsights(@RequestBody PostInsightsEventRequest request,
                                             @RequestParam(value = "channel", required = false) String channel){
        socialReportService.getPostInsights(request);
        return new ResponseEntity<>(HttpStatus.OK);
    }


    /**
     * Get insights from social channel for pages
     * @param channel
     * @return
     * @throws Exception
     */
    @PutMapping("/{channel}/page-insights")
    public ResponseEntity<Void> getPageInsightsForSocialChannel(@RequestBody SocialScanEventDTO socialScanEventDTO,
                                                             @PathVariable("channel") String channel)  {
            socialReportService.getPageInsightsForSocialChannel(socialScanEventDTO, channel);

        return new ResponseEntity<>(HttpStatus.OK);
    }

    @GetMapping("/{channel}/daily-sync")
    public ResponseEntity<Void> dailySyncForPostInsights(@PathVariable("channel") String channel){
        socialReportService.dailySyncForPostInsights(channel);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @GetMapping("/story-insight-sync")
    public ResponseEntity<Void> insightSyncForIgStory(){
        socialReportService.insightSyncForIgStory();
        return new ResponseEntity<>(HttpStatus.OK);
    }
    @PostMapping("audit/acknowledge")
    public ResponseEntity<?> acknowledgeAuditEvent(@RequestBody SocialInsightsAuditDto data) {
        socialReportService.updateAuditEvent(data);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * This API will be called via nifi -  social_page_scanned event
     * It will get all posts for the page id
     * @param channel
     * @param data
     * @return
     */
    @PostMapping("/{channel}/posts")
    public ResponseEntity<?> initiatePostsFetch(@PathVariable("channel") String channel, @RequestBody SocialScanEventDTO data) {
        socialReportService.initiatePostsFetch(channel, data);
        return new ResponseEntity<>(HttpStatus.OK);
    }


    @PostMapping("/{channel}/new-posts")
    public ResponseEntity<Void> newPostFromBe(@RequestBody NewFbPostData newFbPostData,@PathVariable("channel") String channel) throws IOException {
        socialReportService.newPostFromBe(channel,newFbPostData);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * Remove mapping from business posts table
     * @param channelPageRemoved
     * @return
     */
    @PutMapping("/remove/page")
    public ResponseEntity<Void> removePage(@RequestBody List<ChannelPageRemoved> channelPageRemoved){
        socialReportService.removePage(channelPageRemoved);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * Remove mapping from business posts table
     * @param locationPageMappingRequest
     * @return
     */
    @PutMapping("/{channel}/remove/mapping")
    public ResponseEntity<Void> removeMapping(@RequestBody List<LocationPageMappingRequest> locationPageMappingRequest,@PathVariable("channel") String channel){
        socialReportService.removeMapping(locationPageMappingRequest,channel);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * add mapping to business posts table
     * @param locationPageMappingRequest
     * @return
     */
    @PutMapping("/{channel}/page/mapping")
    public ResponseEntity<Void> pageMapping(@RequestBody LocationPageMappingRequest locationPageMappingRequest,@PathVariable("channel") String channel){
        socialReportService.addMapping(locationPageMappingRequest,channel);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     *
     */
    @PutMapping("/page-id")
    public ResponseEntity<Void> populateBEPostId(@RequestParam("days") int days,@RequestParam("size") int size,@RequestParam("limit") boolean limit){
        socialReportService.populateBEPostId(days,size,limit);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping("cdn-upload/event")
    public ResponseEntity<Void> consumeCDNUploadEvent(@RequestBody CDNEventRequest eventRequest) {
        socialReportService.consumeCDNUploadEvent(eventRequest);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping("/cdn-upload-page/event")
    public ResponseEntity<Void> consumeCDNUploadEventForPage(@RequestBody CDNEventRequest eventRequest) {
        socialReportService.consumeCDNUploadEventForPage(eventRequest);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @GetMapping("/cdn-migration-init")
    public ResponseEntity<Void> cdnMigration(@RequestParam("days") int days, @RequestParam("source") int src){
        socialReportService.cdnMigration(days,src);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping("/cdn/posts/fetch")
    public ResponseEntity<Void> postFetch(@RequestBody CDNMigrationAccount migrationAccount){
        socialReportService.postFetch(migrationAccount);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping("cdn-migrate/event")
    public ResponseEntity<Void> consumeCDNMigrateEvent(@RequestBody CDNEventRequest eventRequest) {
        socialReportService.consumeCDNMigrateEvent(eventRequest);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping(value = "/ig/story-insight-event")
	public @ResponseBody ResponseEntity<Void> consumeIGStoryInsightEvent(@RequestBody InstagramEventRequest instagramEventRequest) {
        socialReportService.consumeIGStoryInsightEvent(instagramEventRequest);
		return new ResponseEntity<>(HttpStatus.OK);
	}

    @PostMapping("/ig/story/refresh")
    public ResponseEntity<?> refreshIgPosts(@RequestBody IgStoryEventRequest igStoryEventRequest) {
        socialReportService.refreshIgPosts(igStoryEventRequest);
        return new ResponseEntity<>(HttpStatus.OK);
    }
    @GetMapping("/ig/engagement/backfill/init")
    public ResponseEntity<Void> backFillIgEngagementInit() {
        socialReportService.backFillIgEngagementInit();
        return new ResponseEntity<>(HttpStatus.OK);
    }
    @PostMapping("/ig/engagement/backfill")
    public ResponseEntity<Void> backFillIgEngagement(@RequestBody IgBackFillInsightReq igBackFillInsightReq) {
        socialReportService.backFillIgEngagement(igBackFillInsightReq);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
