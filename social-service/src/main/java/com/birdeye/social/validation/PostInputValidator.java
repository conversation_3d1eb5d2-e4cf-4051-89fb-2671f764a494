package com.birdeye.social.validation;

import com.birdeye.social.constant.PostActivityType;
import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.model.PostingPageScheduler;
import com.birdeye.social.model.SocialPostInputMessageRequest;
import com.birdeye.social.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;
import org.springframework.validation.Errors;
import org.springframework.validation.Validator;
import java.util.Map;
import java.util.Set;

@Component
public class PostInputValidator implements Validator {

    private static String INSTAGRAM_MESSAGE = "Media must be present for posting on instagram";

    private static String YOUTUBE_MESSAGE = "Video must be present for posting on youtube";

    private static String FTLG_MESSAGE = "text or media must be present for posting";

    private static String GMB_VIDEO_MESSAGE = "video is not allowed for posting on google";

    @Override
    public boolean supports(Class<?> aClass) {
        return SocialPostInputMessageRequest.class.equals(aClass);
    }

    @Override
    public void validate(Object o, Errors errors) {
        SocialPostInputMessageRequest socialPostInputMessageRequest = (SocialPostInputMessageRequest) o;

        Map<String, PostingPageScheduler> postingPageSchedulerMap = socialPostInputMessageRequest.getPostingSites();

        if(PostActivityType.RESCHEDULED.getName().equals(socialPostInputMessageRequest.getActivity())) {
            return;
        }

        if(MapUtils.isEmpty(postingPageSchedulerMap)) {
            throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, "posting sites cannot be null");
        }

        Set<String> channels = postingPageSchedulerMap.keySet();
        boolean isImagePresent = CollectionUtils.isNotEmpty(socialPostInputMessageRequest.getImages());
        boolean isVideoPresent = CollectionUtils.isNotEmpty(socialPostInputMessageRequest.getVideos());
        boolean isTextPresent = StringUtils.isNotEmpty(socialPostInputMessageRequest.getPostText());

        if(channels.contains(SocialChannel.INSTAGRAM.getName())) {
            if(!isImagePresent && !isVideoPresent) {
                throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, INSTAGRAM_MESSAGE);
            }
        }
        if(channels.contains(SocialChannel.YOUTUBE.getName())) {
            if(!isVideoPresent) {
                throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, YOUTUBE_MESSAGE);
            }
        }
        if(channels.contains(SocialChannel.FACEBOOK.getName()) || channels.contains(SocialChannel.TWITTER.getName())
                || channels.contains(SocialChannel.LINKEDIN.getName()) || channels.contains(SocialChannel.GOOGLE.getName())) {
            if(!isImagePresent && !isVideoPresent && !isTextPresent) {
                throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, FTLG_MESSAGE);
            }
            if(channels.contains(SocialChannel.GOOGLE.getName()) && isVideoPresent) {
                throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, GMB_VIDEO_MESSAGE);
            }
        }
    }

    public void checkIfValidateAndValidate(Object request) {
        if(supports(request.getClass())) {
            validate(request, null);
        }
    }
}
