package com.birdeye.social.service.SocialReportService.ES;

import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.Constants;
import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.dao.*;
import com.birdeye.social.dto.BusinessLiteDTO;
import com.birdeye.social.dto.EsPageDataPoint;
import com.birdeye.social.dto.EsPostDataPoint;
import com.birdeye.social.insights.ES.Request.*;
import com.birdeye.social.insights.Facebook.ExternalAPIResponse.Data;
import com.birdeye.social.model.ai_post.ReferencePostDetails;
import com.birdeye.social.trends.TrendsReportRequest;
import com.birdeye.social.utils.DateTimeUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.bucket.filter.FilterAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.histogram.Histogram;
import org.elasticsearch.search.aggregations.bucket.histogram.Histogram.Bucket;
import com.birdeye.social.entities.SocialPost;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.exception.SocialBirdeyeException;
import com.birdeye.social.insights.*;
import com.birdeye.social.insights.ES.*;
import com.birdeye.social.insights.ES.Response.ESInsightResponse;
import com.birdeye.social.insights.Facebook.PagePostData;
import com.birdeye.social.insights.Facebook.PostDataAndInsightResponse;
import com.birdeye.social.insights.constants.ElasticConstants;
import com.birdeye.social.insights.constants.GroupByType;
import com.birdeye.social.insights.constants.InsightsConstants;
import com.birdeye.social.model.BackfillInsightReq;
import com.birdeye.social.model.BackfillRequest;
import com.birdeye.social.service.EsService;
import com.birdeye.social.service.SocialReportService.Converter.ReportDataConverter;
import com.birdeye.social.service.SocialTagService;
import com.birdeye.social.sro.SocialTagBasicDetail;
import com.birdeye.social.utils.InsightsReportUtil;
import com.birdeye.social.utils.JSONUtils;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Functions;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.*;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.MultiBucketsAggregation;
import org.elasticsearch.search.aggregations.bucket.filter.ParsedFilter;
import org.elasticsearch.search.aggregations.bucket.histogram.DateHistogramInterval;
import org.elasticsearch.search.aggregations.bucket.histogram.ParsedDateHistogram;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedLongTerms;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedStringTerms;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedTerms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.metrics.*;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.ParsedSum;
import org.elasticsearch.search.aggregations.metrics.ParsedTopHits;
import org.elasticsearch.search.aggregations.metrics.ParsedValueCount;
import org.elasticsearch.search.aggregations.metrics.TopHitsAggregationBuilder;
import org.elasticsearch.search.aggregations.pipeline.BucketSortPipelineAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.birdeye.social.constant.Constants.*;
import static com.birdeye.social.constant.EngageV2FeedTypeEnum.AD_COMMENT;
import static com.birdeye.social.constant.EngageV2FeedTypeEnum.COMMENT;

@Service
public class ReportEsServiceImpl implements ReportsEsService {

    private static final String DATE_WITH_DASH_STRING = "yyyy-MM-dd HH:mm:ss";
    public static final String POST_INSIGHT_INDEX = "post_insight";
    @Autowired
    ESService esService;
    @Autowired
    private EsService esExecuteService;

    @Autowired
    EsService esCommonService;

    @Autowired
    ReportDataConverter reportDataConverter;

    @Autowired
    IBusinessCoreService businessCoreService;

    @Autowired
    private SocialFBPageRepository socialFBPageRepository;
    @Autowired
    private BusinessInstagramAccountRepository businessInstagramAccountRepository;
    @Autowired
    SocialTwitterAccountRepository twitterAccountRepository;
    @Autowired
    private BusinessGMBLocationRawRepository gmbRepo;
    @Autowired
    private BusinessLinkedinPageRepository businessLinkedinPageRepo;
    @Autowired
    private SocialTwitterAccountRepository socialTwitterAccountRepository;
    @Autowired
    private BusinessTiktokAccountsRepository businessTiktokAccountsRepository;
    @Autowired
    private SocialPostRepository socialPostRepository;

    @Autowired
    private SocialTagService socialTagService;

    @Autowired
    @Qualifier(Constants.SOCIAL_TASK_EXECUTOR)
    private ThreadPoolTaskExecutor executor;

    private static final Logger log = LoggerFactory.getLogger(ReportEsServiceImpl.class);

    private final String dateFormatterString = "yyyy-MM-dd HH:mm:ss";

    private final String feedDateFormatterString = "yyyy-MM-dd'T'HH:mm:ss";
    private final String usDateFormatString = "MM/dd/yyy";
    private final String dateForString = "MM/dd/yyyy";
    private static final String CURRENT_DATA = "current_data";
    private static final String PREV_DATA = "prev_data";
    private static final String DATA = "data";
    private static final String HISTOGRAM = "histogram";

    private static final String MESSAGE_SENT = "message_sent";

    private static final String MESSAGE_RECEIVED = "message_received";

    private static final String REPORT_DATA = "report_data";
    private static final String PAGE_ID = "page_id";


    private static final int FROM = 0;
    private static final int SIZE = 100;
    public static final Integer MAX_SELF_PAGE_LIMIT = 10000;



    @Override
    public PageInsightV2EsData getPageInsightDataFromEsChannelWise(InsightsESRequest request){
        PageInsightV2EsData pageInsightEsData = new PageInsightV2EsData();
        try {
            List<? extends Histogram.Bucket> buckets = getCommonInsightRequest(request, pageInsightEsData);
            if(Objects.isNull(buckets)) return pageInsightEsData;
            pageInsightEsData.setBuckets(buckets);
        }catch (Exception e){
            log.error("Exception occurred while fetching data from es channelwise : {}",e.getMessage());
            throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_FETCH_FB_INSIGHTS,e.getMessage());
        }
        return pageInsightEsData;
    }

    @Override
    public PageReportEsData getPageReportDataFromEsChannelWise(InsightsESRequest request){
        PageReportEsData pageInsightEsData = new PageReportEsData();
        try {
            List<? extends Terms.Bucket> buckets = getCommonReportInsightRequest(request);
            if(Objects.isNull(buckets)) return pageInsightEsData;
            if(SearchTemplate.REPORT_SENT_MESSAGE_VOLUME.equals(request.getSearchTemplate())){
                pageInsightEsData.setSentMessage(buckets);
            } else if (SearchTemplate.REPORT_RECEIVED_MESSAGE_VOLUME.equals(request.getSearchTemplate())){
                pageInsightEsData.setReceivedMessage(buckets);
            } else {
                // default case
                pageInsightEsData.setBuckets(buckets);
            }
        }catch (Exception e){
            log.error("Exception occurred while fetching data from es channelwise : {}",e.getMessage(), e);
            throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_FETCH_FB_INSIGHTS,e.getMessage());
        }
        return pageInsightEsData;
    }

    @Override
    public List<ProfilePerformanceExcelResponse> getProfileReportData(Map<String, PageReportEsData> map, InsightsRequest insightsRequest) {
        List<ProfilePerformanceExcelResponse> responses = new ArrayList<>();
        SearchTemplate searchTemplate = SearchTemplate.searchTemplate(insightsRequest.getReportType());
        try {
            for (Map.Entry<String, PageReportEsData> entry : map.entrySet()) {
                String channel = entry.getKey();
//                PageReportEsData pageInsightEsData = entry.getValue();
                List<? extends Terms.Bucket> net = entry.getValue().getBuckets();
                SimpleDateFormat dateFor = new SimpleDateFormat(dateForString);

                switch (searchTemplate) {
                    case REPORT_AUDIENCE_GROWTH:
                        getAudienceGrowthForReport(net, responses, channel, dateFor);
                        break;
                    case REPORT_IMPRESSION:
                        getImpressionForReport(net, responses, channel, dateFor);
                        break;
                    case REPORT_ENGAGEMENT:
                        getEngagementForReport(net, responses, channel, dateFor);
                        break;
                    case REPORT_ENGAGEMENT_RATE:
                        getEngagementRateForReport(net, responses, channel, dateFor);
                        break;
                    case REPORT_VIDEO_VIEWS:
                        getVideoViewsForReport(net, responses, channel, dateFor);
                        break;
                    case REPORT_PUBLISHED_POSTS:
                        getPublishedPostsForReport(net, responses, channel, dateFor);
                        break;
                    case REPORT_MESSAGE_VOLUME:
                        responses.addAll(getMessageVolumeDataPoints(entry.getValue().getSentMessage(), entry.getValue().getReceivedMessage(), channel, dateFor));
                        break;
                    default:
                        log.info("Profile report : Invalid search template {}", searchTemplate);
                        break;
                }
            }
            return responses;
        } catch (Exception ex) {
            log.info("Something went wrong while parsing report data for profile performance excel report: {}", map,  ex);
            return new ArrayList<>();
        }
    }

    @Override
    public BoolQueryBuilder prepareQueryToGetData(BackfillRequest backfillRequest,String pageId) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.filter(QueryBuilders.termQuery("page_id", pageId));
        boolQueryBuilder.filter(new RangeQueryBuilder("day").
                gt(simpleDateFormat.format(backfillRequest.getBackfillInsightReq().getStartDate())));
        return boolQueryBuilder;
    }

    @Override
    public List<ESPageRequest> getDataFromEsIndex(BoolQueryBuilder boolQueryBuilder, int size, String index) {
        List<ESPageRequest> esPageDataPoints = new ArrayList<>();
        SearchRequest searchRequest = new SearchRequest();
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.size(size);
        searchSourceBuilder.query(boolQueryBuilder);
        searchRequest.source(searchSourceBuilder);
        searchRequest.indices(index);
        try {
            SearchResponse searchResponse = esCommonService.search(searchRequest);
            SearchHit[] searchHits = searchResponse.getHits().getHits();
            for(SearchHit searchHit: searchHits) {
                ESPageRequest esPageDataPoint = JSONUtils.fromJSON(searchHit.getSourceAsString(), ESPageRequest.class);
                esPageDataPoints.add(esPageDataPoint);
            }
        }catch (Exception e){
            throw new BirdeyeSocialException("Exception occurred while back fill",e);
        }
        return esPageDataPoints;
    }

    private List<ProfilePerformanceExcelResponse> getMessageVolumeDataPoints(List<? extends Terms.Bucket> sentBucket,  List<? extends Terms.Bucket> receivedBucket, String channel, SimpleDateFormat dateFor) {
        List<ProfilePerformanceExcelResponse> response = new ArrayList<>();
        List<ProfilePerformanceExcelResponse> sent = new ArrayList<>();
        List<ProfilePerformanceExcelResponse> received = new ArrayList<>();

        Map<String, List<Terms.Bucket>> sentBuckets = new HashMap<>();
        Map<String, List<Terms.Bucket>> receivedBuckets = new HashMap<>();


        List<? extends Terms.Bucket> msgSentBucket =  CollectionUtils.isNotEmpty(sentBucket)
                    ?  sentBucket : null;
        List<? extends Terms.Bucket> msgReceivedBucket = CollectionUtils.isNotEmpty(receivedBucket)
                    ? receivedBucket : null;

        if(CollectionUtils.isNotEmpty(msgSentBucket)) {
                sentBuckets.put(channel, (List<Terms.Bucket>) msgSentBucket);
        }
        if(CollectionUtils.isNotEmpty(msgReceivedBucket)) {
                receivedBuckets.put(channel, (List<Terms.Bucket>) msgReceivedBucket);
        }

        if(MapUtils.isEmpty(sentBuckets) && MapUtils.isEmpty(receivedBuckets)) {
            return response;
        }

        for(Map.Entry<String, List<Terms.Bucket>> entry : sentBuckets.entrySet()) {
            List<Terms.Bucket> sentMessageBucket = entry.getValue();
            repeatedDataPointsForSentMessage(sent, sentMessageBucket, entry.getKey(), dateFor);
        }


        for(Map.Entry<String, List<Terms.Bucket>> entry : receivedBuckets.entrySet()) {
            List<Terms.Bucket> receivedMessageBucket = entry.getValue();
            repeatedDataPointsForReceivedMessage(received, receivedMessageBucket, entry.getKey(), dateFor);
        }

        return mergeObjects(sent, received);

    }

    public static List<ProfilePerformanceExcelResponse> mergeObjects(List<ProfilePerformanceExcelResponse> sent, List<ProfilePerformanceExcelResponse> received) {
        Map<String, ProfilePerformanceExcelResponse> map = new HashMap<>();
        List<ProfilePerformanceExcelResponse> resultList = new ArrayList<>();

        // Populate map with elements from list1
        for (ProfilePerformanceExcelResponse obj : sent) {
            map.put(obj.getProfileId(), obj);
        }

        // Merge elements from list2
        for (ProfilePerformanceExcelResponse obj : received) {
            if (map.containsKey(obj.getProfileId())) {
                ProfilePerformanceExcelResponse mergedObject = new ProfilePerformanceExcelResponse(); // Merge logic
                ProfilePerformanceExcelResponse existingObject = map.get(obj.getProfileId());
                mergedObject.setTotalSentMessages(existingObject.getTotalSentMessages());
                mergedObject.setTotalReceivedMessages(obj.getTotalReceivedMessages());
                mergedObject.setLabel(obj.getLabel());
                mergedObject.setChannel(obj.getChannel());
                mergedObject.setTotalReceivedMessages(obj.getTotalReceivedMessages());
                mergedObject.setProfileId(existingObject.getProfileId());

                mergedObject.setTotalMessages(mergedObject.getTotalSentMessages() + mergedObject.getTotalReceivedMessages());

                resultList.add(mergedObject);
            } else {
                resultList.add(obj);
            }
        }

        // Add elements from list1 that are not present in list2
        for (ProfilePerformanceExcelResponse obj : sent) {
            if (!map.containsKey(obj.getProfileId())) {
                obj.setTotalMessages(obj.getTotalSentMessages());
                obj.setTotalReceivedMessages(0);
                resultList.add(obj);
            }
        }

        return resultList;
    }

    private void repeatedDataPointsForSentMessage(List<ProfilePerformanceExcelResponse> sent, List<Terms.Bucket> sentBuckets, String channel, SimpleDateFormat dateFor ) {
        for(Terms.Bucket bucket : sentBuckets) {
            String pageId = bucket.getKeyAsString();
            ParsedDateHistogram dateHistogram = bucket.getAggregations().get("sentMessage");

            if(CollectionUtils.isEmpty(dateHistogram.getBuckets())) {
                continue;
            }

            dateHistogram.getBuckets().forEach(b -> {
                ProfilePerformanceExcelResponse pageInsightDataPoint = new ProfilePerformanceExcelResponse();
                pageInsightDataPoint.setTotalSentMessages((int) b.getDocCount());
                Date date = new Date();
                try {
                    date = new SimpleDateFormat(dateFormatterString).parse(b.getKeyAsString().trim());
                } catch (ParseException e) {
                    log.error("Error while parsing date",e);
                }
                pageInsightDataPoint.setProfileId(pageId);
                pageInsightDataPoint.setLabel(dateFor.format(date));
                pageInsightDataPoint.setChannel(channel);
                sent.add(pageInsightDataPoint);
            });

        }
    }



    private void repeatedDataPointsForReceivedMessage(List<ProfilePerformanceExcelResponse> received, List<Terms.Bucket> receivedMessageBucket, String channel, SimpleDateFormat dateFor ) {
        for(Terms.Bucket bucket : receivedMessageBucket) {
            String pageId = bucket.getKeyAsString();
            ParsedDateHistogram dateHistogram = bucket.getAggregations().get("receivedMessage");

            if(CollectionUtils.isEmpty(dateHistogram.getBuckets())) {
                continue;
            }

            dateHistogram.getBuckets().forEach(b -> {
                ProfilePerformanceExcelResponse pageInsightDataPoint = new ProfilePerformanceExcelResponse();
                pageInsightDataPoint.setTotalReceivedMessages((int) b.getDocCount());
                Date date = new Date();
                try {
                    date = new SimpleDateFormat(dateFormatterString).parse(b.getKeyAsString().trim());
                } catch (ParseException e) {
                    log.error("Error while parsing date",e);
                }
                pageInsightDataPoint.setProfileId(pageId);
                pageInsightDataPoint.setLabel(dateFor.format(date));
                pageInsightDataPoint.setChannel(channel);
                received.add(pageInsightDataPoint);
            });

        }
    }


    private void getAudienceGrowthForReport(List<? extends Terms.Bucket> net, List<ProfilePerformanceExcelResponse> responses, String channel, SimpleDateFormat dateFor) throws ParseException {
        if(CollectionUtils.isEmpty(net)) return;
        net.forEach(bucket -> {
            try {
                String pageId = bucket.getKeyAsString();

                Aggregation histogramList =  bucket.getAggregations().get(HISTOGRAM);

                List<? extends Histogram.Bucket> buckets = ((ParsedDateHistogram) histogramList).getBuckets();

                if(CollectionUtils.isNotEmpty(buckets)) {
                    buckets.forEach(v -> {
                        Date date = null;
                        try {
                            date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(v.getKeyAsString().trim());
                        } catch (ParseException e) {
                            log.info("Exception occurred while parsing date : {}", e.getMessage());
                        }

                        ProfilePerformanceExcelResponse esResponse = new ProfilePerformanceExcelResponse();
                        esResponse.setChannel(channel);
                        esResponse.setProfileId(pageId);
                        esResponse.setLabel(dateFor.format(date));
                        ParsedSum followerGain = v.getAggregations().get("followerGain");

                        ParsedSum followerLost = v.getAggregations().get("followerLost");

                        esResponse.setNetAudienceGrowth((int) followerGain.getValue() - (int) followerLost.getValue());

                        ParsedTopHits netFollowers = v.getAggregations().get("followers");
                        netFollowers.getHits().forEach(e -> {
                            EsPageDataPoint esPageDataPoint = JSONUtils.fromJSON(e.getSourceAsString(), EsPageDataPoint.class);
                            esResponse.setTotalAudience(esPageDataPoint.getTotal_follower_count());
                        });

                        responses.add(esResponse);

                    });
                }
            } catch (Exception e) {
                log.info("Exception occurred : {}", e.getMessage());
            }
        });
    }

    private void getAudienceGrowthForChannelSpecificReport(List<? extends Terms.Bucket> net, List<ProfilePerformanceExcelResponse> responses,SimpleDateFormat dateFor, String channel) throws ParseException {
        if(CollectionUtils.isEmpty(net)) return;
        net.forEach(bucket -> {
            try {
                String pageId = bucket.getKeyAsString();

                Aggregation histogramList =  bucket.getAggregations().get(HISTOGRAM);

                List<? extends Histogram.Bucket> buckets = ((ParsedDateHistogram) histogramList).getBuckets();

                if(CollectionUtils.isNotEmpty(buckets)) {
                    buckets.forEach(v -> {
                        Date date = null;
                        try {
                            date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(v.getKeyAsString().trim());
                        } catch (ParseException e) {
                            log.info("Exception occurred while parsing date : {}", e.getMessage());
                        }

                        ProfilePerformanceExcelResponse esResponse = new ProfilePerformanceExcelResponse();
                        esResponse.setChannel(channel);
                        esResponse.setProfileId(pageId);
                        esResponse.setLabel(dateFor.format(date));
                        ParsedSum followerGain = v.getAggregations().get("followerGain");

                        ParsedSum followerLost = v.getAggregations().get("followerLost");
                        esResponse.setFollowerLost((int)followerLost.getValue());
                        esResponse.setNetAudienceGrowth((int) followerGain.getValue() - (int) followerLost.getValue());

                        ParsedTopHits netFollowers = v.getAggregations().get("followers");
                        netFollowers.getHits().forEach(e -> {
                            EsPageDataPoint esPageDataPoint = JSONUtils.fromJSON(e.getSourceAsString(), EsPageDataPoint.class);
                            if(Objects.nonNull(esPageDataPoint) && Objects.nonNull(esPageDataPoint.getTotal_follower_count()))
                                esResponse.setTotalAudience(esPageDataPoint.getTotal_follower_count());
                            else
                                esResponse.setTotalAudience(0);
                        });

                        responses.add(esResponse);

                    });
                }
            } catch (Exception e) {
                log.info("Exception occurred : {}", e.getMessage());
            }
        });
    }

    private void getImpressionForReport(List<? extends Terms.Bucket> net, List<ProfilePerformanceExcelResponse> responses, String channel, SimpleDateFormat dateFor) throws ParseException {
        if(CollectionUtils.isEmpty(net)) return;
        net.forEach(bucket -> {
            try {
                String pageId = bucket.getKeyAsString();

                Aggregation histogramList =  bucket.getAggregations().get(HISTOGRAM);

                List<? extends Histogram.Bucket> buckets = ((ParsedDateHistogram) histogramList).getBuckets();

                if(CollectionUtils.isNotEmpty(buckets)) {
                    buckets.forEach(v -> {
                        Date date = null;
                        try {
                            date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(v.getKeyAsString().trim());
                        } catch (ParseException e) {
                            log.info("Exception occurred while parsing date : {}", e.getMessage());
                        }

                        ProfilePerformanceExcelResponse esResponse = new ProfilePerformanceExcelResponse();
                        esResponse.setChannel(channel);
                        esResponse.setProfileId(pageId);
                        esResponse.setLabel(dateFor.format(date));
                        ParsedSum followerGain = v.getAggregations().get("impression");

                        esResponse.setImpressions((int) followerGain.getValue());
                        responses.add(esResponse);

                    });
                }
            } catch (Exception e) {
                log.info("Exception occurred : {}", e.getMessage());
            }
        });
    }

    private void getEngagementForReport(List<? extends Terms.Bucket> net, List<ProfilePerformanceExcelResponse> responses, String channel, SimpleDateFormat dateFor) throws ParseException {
        if(CollectionUtils.isEmpty(net)) return;
        net.forEach(bucket -> {
            try {
                String pageId = bucket.getKeyAsString();

                Aggregation histogramList =  bucket.getAggregations().get(HISTOGRAM);

                List<? extends Histogram.Bucket> buckets = ((ParsedDateHistogram) histogramList).getBuckets();

                if(CollectionUtils.isNotEmpty(buckets)) {
                    buckets.forEach(v -> {
                        Date date = null;
                        try {
                            date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(v.getKeyAsString().trim());
                        } catch (ParseException e) {
                            log.info("Exception occurred while parsing date : {}", e.getMessage());
                        }

                        ProfilePerformanceExcelResponse esResponse = new ProfilePerformanceExcelResponse();
                        esResponse.setChannel(channel);
                        esResponse.setProfileId(pageId);
                        esResponse.setLabel(dateFor.format(date));
                        ParsedSum engagement = v.getAggregations().get("engagement");
                        ParsedSum likeCount = v.getAggregations().get("likeCount");
                        ParsedSum commentCount = v.getAggregations().get("commentCount");
                        ParsedSum shareCount = v.getAggregations().get("shareCount");

                        esResponse.setEngagement((int) engagement.getValue());
                        esResponse.setCommentCount((int) commentCount.getValue());
                        esResponse.setLikeCount((int) likeCount.getValue());
                        esResponse.setShareCount((int) shareCount.getValue());
                        responses.add(esResponse);

                    });
                }
            } catch (Exception e) {
                log.info("Exception occurred : {}", e.getMessage());
            }
        });
    }

    private void getEngagementRateForReport(List<? extends Terms.Bucket> net, List<ProfilePerformanceExcelResponse> responses, String channel, SimpleDateFormat dateFor) throws ParseException {
        if(CollectionUtils.isEmpty(net)) return;
        net.forEach(bucket -> {
            try {
                String pageId = bucket.getKeyAsString();

                Aggregation histogramList =  bucket.getAggregations().get(HISTOGRAM);

                List<? extends Histogram.Bucket> buckets = ((ParsedDateHistogram) histogramList).getBuckets();

                if(CollectionUtils.isNotEmpty(buckets)) {
                    buckets.forEach(v -> {
                        Date date = null;
                        try {
                            date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(v.getKeyAsString().trim());
                        } catch (ParseException e) {
                            log.info("Exception occurred while parsing date : {}", e.getMessage());
                        }

                        ProfilePerformanceExcelResponse esResponse = new ProfilePerformanceExcelResponse();
                        esResponse.setChannel(channel);
                        esResponse.setProfileId(pageId);
                        esResponse.setLabel(dateFor.format(date));
                        ParsedSum clickCount = v.getAggregations().get("clickCount");

//                        esResponse.setPostLinkClicks((int) clickCount.getValue());

                        ParsedSum engagementRate = v.getAggregations().get("engagementRate");
                        esResponse.setEng(formatToTwoDecimalPlaces(engagementRate.getValue()));


                        responses.add(esResponse);

                    });
                }
            } catch (Exception e) {
                log.info("Exception occurred : {}", e.getMessage());
            }
        });
    }

    private void getVideoViewsForReport(List<? extends Terms.Bucket> net, List<ProfilePerformanceExcelResponse> responses, String channel, SimpleDateFormat dateFor) throws ParseException {
        if(CollectionUtils.isEmpty(net)) return;
        net.forEach(bucket -> {
            try {
                String pageId = bucket.getKeyAsString();

                Aggregation histogramList =  bucket.getAggregations().get(HISTOGRAM);

                List<? extends Histogram.Bucket> buckets = ((ParsedDateHistogram) histogramList).getBuckets();

                if(CollectionUtils.isNotEmpty(buckets)) {
                    buckets.forEach(v -> {
                        Date date = null;
                        try {
                            date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(v.getKeyAsString().trim());
                        } catch (ParseException e) {
                            log.info("Exception occurred while parsing date : {}", e.getMessage());
                        }

                        ProfilePerformanceExcelResponse esResponse = new ProfilePerformanceExcelResponse();
                        esResponse.setChannel(channel);
                        esResponse.setProfileId(pageId);
                        esResponse.setLabel(dateFor.format(date));
                        ParsedSum videoViews = v.getAggregations().get("videoViews");

                        esResponse.setVideoViews((int) videoViews.getValue());

                        responses.add(esResponse);
                    });
                }
            } catch (Exception e) {
                log.info("Exception occurred : {}", e.getMessage());
            }
        });
    }

    private void getPublishedPostsForReport(List<? extends Terms.Bucket> net, List<ProfilePerformanceExcelResponse> responses, String channel, SimpleDateFormat dateFor) throws ParseException {
        if(CollectionUtils.isEmpty(net)) return;
        net.forEach(bucket -> {
            try {
                String pageId = bucket.getKeyAsString();

                Aggregation histogramList =  bucket.getAggregations().get(HISTOGRAM);

                List<? extends Histogram.Bucket> buckets = ((ParsedDateHistogram) histogramList).getBuckets();

                if(CollectionUtils.isNotEmpty(buckets)) {
                    buckets.forEach(v -> {
                        Date date = null;
                        try {
                            date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(v.getKeyAsString().trim());
                        } catch (ParseException e) {
                            log.info("Exception occurred while parsing date : {}", e.getMessage());
                        }

                        ProfilePerformanceExcelResponse esResponse = new ProfilePerformanceExcelResponse();
                        esResponse.setChannel(channel);
                        esResponse.setProfileId(pageId);
                        esResponse.setLabel(dateFor.format(date));
                        Long postCount = v.getDocCount();

                        esResponse.setPublishedPosts(Objects.isNull(postCount) ? 0 : Math.toIntExact(postCount));

                        responses.add(esResponse);
                    });
                }
            } catch (Exception e) {
                log.info("Exception occurred : {}", e.getMessage());
            }
        });
    }

    private List<ProfilePerformanceExcelResponse> getMessagesVolumeForReport(List<? extends Terms.Bucket> sentMessageBucket, List<? extends Terms.Bucket> receivedMessageBucket, String channel, SimpleDateFormat dateFor) throws ParseException {
        List<ProfilePerformanceExcelResponse> responses = new ArrayList<>();

        if(CollectionUtils.isEmpty(sentMessageBucket)) {
            sentMessageBucket = new ArrayList<>();
        }

        if(CollectionUtils.isEmpty(receivedMessageBucket)) {
            receivedMessageBucket = new ArrayList<>();
        }

        Map<String, Terms.Bucket> map = new HashMap<>();
        List<Terms.Bucket> resultList = new ArrayList<>();

        // Populate map with elements from list1
        for (Terms.Bucket obj : sentMessageBucket) {
            map.put(obj.getKeyAsString(), obj);
        }

        // Merge elements from list2
        for (Terms.Bucket obj : receivedMessageBucket) {
            if (map.containsKey(obj.getKeyAsString())) {
                Terms.Bucket mergedObject = obj; // Merge logic
                resultList.add(mergedObject);
            } else {
                resultList.add(obj);
            }
        }

        // Add elements from list1 that are not present in list2
        for (Terms.Bucket obj : sentMessageBucket) {
            if (!map.containsKey(obj.getKeyAsString())) {
                resultList.add(obj);
            }
        }


//        return resultList;

//        net.forEach(bucket -> {
//            try {
//                String pageId = bucket.getKeyAsString();
//
//                Aggregation histogramList =  bucket.getAggregations().get(HISTOGRAM);
//
//                List<? extends Histogram.Bucket> buckets = ((ParsedDateHistogram) histogramList).getBuckets();
//
//                List<? extends Histogram.Bucket> compareBuckets = ((ParsedDateHistogram) histogramList).getBuckets();
//
//                List<? extends Histogram.Bucket> compareBucketsList = compareBuckets.stream()
//                        .filter(v -> v.getKeyAsString().equalsIgnoreCase(pageId)).collect(Collectors.toList());
//
//                if(CollectionUtils.isNotEmpty(buckets)) {
//                    buckets.forEach(v -> {
//                        Date date = null;
//                        try {
//                            date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(v.getKeyAsString().trim());
//                        } catch (ParseException e) {
//                            log.info("Exception occurred while parsing date : {}", e.getMessage());
//                        }
//
//                       Optional<? extends Histogram.Bucket> optionalBucket =
//                               compareBucketsList.stream().filter(comp -> v.getKeyAsString().equalsIgnoreCase(comp.getKeyAsString())).findFirst();
//
//
//                        ProfilePerformanceExcelResponse esResponse = new ProfilePerformanceExcelResponse();
//                        esResponse.setChannel(channel);
//                        esResponse.setProfileId(pageId);
//                        esResponse.setLabel(dateFor.format(date));
//                        ParsedSum videoViews = v.getAggregations().get("videoViews");
//
//                        esResponse.setVideoViews((int) videoViews.getValue());
//
//                        responses.add(esResponse);
//                    });
//                }
//            } catch (Exception e) {
//                log.info("Exception occurred : {}", e.getMessage());
//            }
//        });
        return responses;
    }


    private List<? extends Terms.Bucket> getCommonReportInsightRequest(InsightsESRequest request) {
        ESInsightResponse esPageInsightResponse = esService.searchInsightTemplate(request);
        if(Objects.isNull(esPageInsightResponse.getResponse())){
            log.info("Exception occurred while getting page insights for search template for common:{}",request.getSearchTemplate());
            throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_FETCH_FB_INSIGHTS);
        }

        Aggregations aggregations = esPageInsightResponse.getResponse().getAggregations();
        if(Objects.isNull(aggregations)){
            log.info("getCommonInsightRequest Aggregations are empty");
            return null;
        }


        ParsedStringTerms reportData = aggregations.get(REPORT_DATA);
        if(Objects.nonNull(reportData)) {
            return reportData.getBuckets();
        }

        return null;
    }




    @Override
    public PageInsightV2EsData getTotalAudFromEsChannelWise(InsightsESRequest request, PageInsightV2EsData pageInsightEsData){
        try {
            ESInsightResponse esPageInsightResponse = esService.searchInsightTemplate(request);
            if(Objects.isNull(esPageInsightResponse.getResponse())){
                log.info("Exception occurred while getting page insights for search template :{}",request.getSearchTemplate());
                throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_FETCH_FB_INSIGHTS);
            }

            Aggregations aggregations = esPageInsightResponse.getResponse().getAggregations();
            if(Objects.isNull(aggregations)){
                log.info("Aggregations are empty");
                return null;
            }
            ParsedStringTerms dateHistogramAggregation = aggregations.get("totat_follower_count");
            log.info("dateHistogramAggregation data for search template : {}",request.getSearchTemplate());
            if (Objects.isNull(dateHistogramAggregation.getBuckets()) && dateHistogramAggregation.getBuckets().size() == 0){
                log.info("DateHistogramAggregation size is empty for search template :{}",request.getSearchTemplate());
                //return pageInsightEsData;
            }
            log.info("dateHistogramAggregation bucket data for search template  : {}",request.getSearchTemplate());
            List<? extends Terms.Bucket> buckets = dateHistogramAggregation.getBuckets();
            if(Objects.isNull(buckets)) return pageInsightEsData;
            Integer total = pageInsightEsData.getTotal() != null ? pageInsightEsData.getTotal() : 0;
            AtomicReference<Integer> pagesTotalFollowerCount = new AtomicReference<>(0);
            for (Terms.Bucket bucket : buckets) {
                try {
                    ParsedTopHits s1 = bucket.getAggregations().get("latest_time_stamp");
                    Arrays.stream(s1.getHits().getHits()).forEach(e -> {
                        EsPageDataPoint esPageDataPoint = JSONUtils.fromJSON(e.getSourceAsString(), EsPageDataPoint.class);
                        pagesTotalFollowerCount.updateAndGet(v -> v + (Objects.nonNull(esPageDataPoint.getTotal_follower_count()) ?
                                esPageDataPoint.getTotal_follower_count() : 0));
                    });
                } catch (Exception e) {
                    log.info("getTotalAudFromEsChannelWise Parse exception occurred : {}", e.getMessage());
                }
            }
            pageInsightEsData.setTotal(total + pagesTotalFollowerCount.get());

        }catch (Exception e){
            log.info("Exception occurred while fetching data from es : {}",e.getMessage());
            throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_FETCH_FB_INSIGHTS,e.getMessage());
        }
        return pageInsightEsData;
    }

    @Override
    public PageInsightV2EsData getMessageVolumeFromEsChannelWise(InsightsESRequest request, PageInsightV2EsData pageInsightEsData){
        try {
            ESInsightResponse esPageInsightResponse = esService.searchInsightTemplate(request);
            if(Objects.isNull(esPageInsightResponse.getResponse())){
                log.info("Exception occurred while getting page insights for search template :{}",request.getSearchTemplate());
                throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_FETCH_FB_INSIGHTS);
            }

            Aggregations aggregations = esPageInsightResponse.getResponse().getAggregations();
            if(Objects.isNull(aggregations)){
                log.info("Aggregations are empty");
                return null;
            }
            ParsedFilter messageSentAggregation = aggregations.get(MESSAGE_SENT);
            ParsedFilter messageReceivedAggregation = aggregations.get(MESSAGE_RECEIVED);

            pageInsightEsData.setTotalMsgSent((int) messageSentAggregation.getDocCount());
            pageInsightEsData.setTotalMsgReceived((int) messageReceivedAggregation.getDocCount());

            log.info("dateHistogramAggregation data for search template : {}",request.getSearchTemplate());
//            if (Objects.isNull(dateHistogramAggregation.getBuckets()) && dateHistogramAggregation.getBuckets().size() == 0){
//                log.info("DateHistogramAggregation size is empty for search template :{}",request.getSearchTemplate());
//                //return pageInsightEsData;
//            }
            log.info("dateHistogramAggregation bucket data for search template  : {}",request.getSearchTemplate());
            List<? extends Terms.Bucket> buckets = messageSentAggregation.getAggregations().get(MESSAGE_SENT);
            if(Objects.isNull(buckets)) return pageInsightEsData;
            Integer total = pageInsightEsData.getTotal() != null ? pageInsightEsData.getTotal() : 0;
            for (Terms.Bucket bucket : buckets) {
                try {
                    ParsedTopHits s1 = bucket.getAggregations().get("latest_time_stamp");
                    Arrays.stream(s1.getHits().getHits()).forEach(e -> {
                        EsPageDataPoint esPageDataPoint = JSONUtils.fromJSON(e.getSourceAsString(), EsPageDataPoint.class);
                        pageInsightEsData.setTotal(total + esPageDataPoint.getTotal_follower_count());

                    });
                } catch (Exception e) {
                    log.info("getTotalAudFromEsChannelWise Parse exception occurred : {}", e.getMessage());
                }
            }

        }catch (Exception e){
            log.info("Exception occurred while fetching data from es : {}",e.getMessage());
            throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_FETCH_FB_INSIGHTS,e.getMessage());
        }
        return pageInsightEsData;
    }

    private List<? extends Histogram.Bucket> getCommonInsightRequest(InsightsESRequest request, PageInsightV2EsData pageInsightEsData) {
        ESInsightResponse esPageInsightResponse = esService.searchInsightTemplate(request);
        if (Objects.isNull(esPageInsightResponse.getResponse())) {
            log.info("Exception occurred while getting page insights for search template for common:{}", request.getSearchTemplate());
            throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_FETCH_FB_INSIGHTS);
        }

        Aggregations aggregations = esPageInsightResponse.getResponse().getAggregations();
        if (Objects.isNull(aggregations)) {
            log.info("getCommonInsightRequest Aggregations are empty");
            return null;
        }

        ParsedFilter prevDataAgg = aggregations.get(PREV_DATA);
        if (Objects.nonNull(prevDataAgg)) {
            log.info("Set prev_data for page insights for search template :{}", request.getSearchTemplate());
            PageInsightData prevData = reportDataConverter.convertEsData(prevDataAgg, request.getSearchTemplate());
            pageInsightEsData.setPrevData(prevData);
        }
        ParsedFilter currentDataAgg = aggregations.get(CURRENT_DATA);
        if (Objects.nonNull(currentDataAgg)) {
            PageInsightData currentData = reportDataConverter.convertEsData(currentDataAgg, request.getSearchTemplate());
            log.info("Added current_data to page insights for search template :{}", request.getSearchTemplate());
            pageInsightEsData.setCurrentData(currentData);
        }


        ParsedFilter messageSentAggregation = aggregations.get(MESSAGE_SENT);
        if (Objects.nonNull(messageSentAggregation)) {
            pageInsightEsData.setTotalMsgSent((int) messageSentAggregation.getDocCount());
            ParsedDateHistogram dateHistogramAggregationMsgSent = messageSentAggregation.getAggregations().get(HISTOGRAM);
            if (Objects.nonNull(dateHistogramAggregationMsgSent.getBuckets()) && dateHistogramAggregationMsgSent.getBuckets().size() != 0) {
                pageInsightEsData.setMsgSentBuckets(dateHistogramAggregationMsgSent.getBuckets());
            }
        }

        ParsedFilter messageReceivedAggregation = aggregations.get(MESSAGE_RECEIVED);
        if (Objects.nonNull(messageReceivedAggregation)) {
            pageInsightEsData.setTotalMsgReceived((int) messageReceivedAggregation.getDocCount());
            ParsedDateHistogram dateHistogramAggregationMsgReceived = messageReceivedAggregation.getAggregations().get(HISTOGRAM);
            if (Objects.nonNull(dateHistogramAggregationMsgReceived.getBuckets()) && dateHistogramAggregationMsgReceived.getBuckets().size() != 0) {
                pageInsightEsData.setMsgReceivedBuckets(dateHistogramAggregationMsgReceived.getBuckets());
            }
        }
        ParsedFilter data = aggregations.get(DATA);
        if (Objects.nonNull(data)) {
            ParsedDateHistogram dateHistogramAggregation = data.getAggregations().get(HISTOGRAM);
            if (Objects.isNull(dateHistogramAggregation.getBuckets()) && dateHistogramAggregation.getBuckets().size() == 0) {
                log.info("DateHistogramAggregation size is empty for search template :{}", request.getSearchTemplate());
                return null;
            }
            log.info("dateHistogramAggregation bucket data for search template  : {}", request.getSearchTemplate());
            return dateHistogramAggregation.getBuckets();
        }

        return null;
    }

    @Override
    public PageInsightEsData getPageInsightDataFromEs(InsightsESRequest request) {
        log.info("Get page insights");
        PageInsightEsData pageInsightEsData = new PageInsightEsData();
        try {
            ESInsightResponse esPageInsightResponse = esService.searchInsightTemplate(request);
            if(Objects.isNull(esPageInsightResponse.getResponse())){
                log.info("Exception occurred while getting page insights for search template :{}",request.getSearchTemplate());
                throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_FETCH_FB_INSIGHTS);
            }

            Aggregations aggregations = esPageInsightResponse.getResponse().getAggregations();
            if(Objects.isNull(aggregations)){
                log.info("Aggregations are empty");
                return pageInsightEsData;
            }

            ParsedFilter prevDataAgg = aggregations.get(PREV_DATA);
            if(Objects.nonNull(prevDataAgg)){
                log.info("Set prev_data for page insights for search template :{}",request.getSearchTemplate());
                PageInsightData prevData = reportDataConverter.convertEsData(prevDataAgg,request.getSearchTemplate());
                pageInsightEsData.setPrevData(prevData);
            }
            ParsedFilter currentDataAgg = aggregations.get(CURRENT_DATA);
            if(Objects.nonNull(currentDataAgg)){
                log.info("Set current_data for page insights for search template:{}",request.getSearchTemplate());
                PageInsightData currentData = reportDataConverter.convertEsData(currentDataAgg,request.getSearchTemplate());
                log.info("Added current_data to page insights for search template :{}",request.getSearchTemplate());
                pageInsightEsData.setCurrentData(currentData);
            }
            log.info("After setting current_data for search template :{}",request.getSearchTemplate());
            ParsedFilter data = aggregations.get(DATA);
            log.info("parsed data : {}",data);
            ParsedDateHistogram dateHistogramAggregation = data.getAggregations().get(HISTOGRAM);
            log.info("dateHistogramAggregation data for search template : {}",request.getSearchTemplate());
            if (Objects.isNull(dateHistogramAggregation.getBuckets()) && dateHistogramAggregation.getBuckets().size() == 0){
                log.info("DateHistogramAggregation size is empty for search template :{}",request.getSearchTemplate());
                return pageInsightEsData;
            }
            log.info("dateHistogramAggregation bucket data for search template  : {}",request.getSearchTemplate());
            List<? extends Histogram.Bucket> buckets = dateHistogramAggregation.getBuckets();
            List<PageInsightDataPoint> fbPageDataPointsList = new ArrayList<>();
            buckets.forEach(bucket -> {
                PageInsightDataPoint fbInsightsDto;
                try {
                    log.info("Bucket as string : {}",bucket.getKeyAsString());
                    Date date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(bucket.getKeyAsString().trim());
                    log.info("date :{}",date);
                    fbInsightsDto = reportDataConverter.
                            preparePageReport(bucket,date,request.getSearchTemplate(), GroupByType.getByType(request.getDataModel().getType()));
                    fbPageDataPointsList.add(fbInsightsDto);
                } catch (ParseException e) {
                    log.info("Parse exception occurred : {}",e.getMessage());
                }
            });
            pageInsightEsData.setPointsList(fbPageDataPointsList);
            log.info("Returned DateHistogramAggregation with size {}"
                    ,fbPageDataPointsList.size());
        }catch (Exception e){
            log.info("Exception occurred while fetching data from es : {}",e.getMessage());
            throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_FETCH_FB_INSIGHTS,e.getMessage());
        }
        return pageInsightEsData;
    }

    @Override
    public List<PageInsightDataPoint> getPageInsightHitsDataFromEs(InsightsESRequest request) {
        List<PageInsightDataPoint> responseList = new ArrayList<>();

        try {
            ESInsightResponse esPageInsightResponse = esService.searchInsightTemplate(request);

            SearchResponse searchResponse = esPageInsightResponse.getResponse();
            SearchHit[] searchHits = searchResponse.getHits().getHits();

            for(SearchHit searchHit: searchHits) {
                EsPageDataPoint esPageDataPoint = JSONUtils.fromJSON(searchHit.getSourceAsString(), EsPageDataPoint.class);

                PageInsightDataPoint pageInsightDataPoint = PageInsightDataPoint.builder()
                        .followersGain(esPageDataPoint.getFollower_gain())
                        .followersLost(esPageDataPoint.getFollower_lost())
                        .likesGain(esPageDataPoint.getLikes_gain())
                        .likesLost(esPageDataPoint.getLikes_lost())
                        .postEngagements(esPageDataPoint.getPost_engagement())
                        .postEngagementTotal(esPageDataPoint.getPost_engagement_total())
                        .postEngRate(esPageDataPoint.getPost_eng_rate())
                        .postReach(esPageDataPoint.getPost_reach())
                        .postGain(esPageDataPoint.getPost_gain())
                        .postImpressions(esPageDataPoint.getPost_impressions())
                        .totalLikes(esPageDataPoint.getTotal_likes())
                        .totalPosts(esPageDataPoint.getPost_total_count())
                        .totalFollowers(esPageDataPoint.getTotal_follower_count())
                        .date(esPageDataPoint.getDay())
                        .clickCount(esPageDataPoint.getClick_count())
                        .otherClickCount(esPageDataPoint.getOther_click_count())
                        .videoViews(esPageDataPoint.getVideo_views())
                        .postCount(esPageDataPoint.getPost_count())
                        .postImpressionTotal(esPageDataPoint.getPost_impression_total())
                        .totalVideoViews(esPageDataPoint.getTotal_video_views())
                        .totalPostCommentCount(esPageDataPoint.getTotal_post_comment_count())
                        .totalPostLikeCount(esPageDataPoint.getTotal_post_like_count())
                        .totalPostShareCount(esPageDataPoint.getTotal_post_share_count())
                        .shareCount(esPageDataPoint.getPost_share_count())
                        .commentCount(esPageDataPoint.getPost_comment_count())
                        .likeCount(esPageDataPoint.getPost_like_count())
                        .build();
                responseList.add(pageInsightDataPoint);
            }
        } catch (Exception e) {
            log.info("Exception occurred while fetching data from es : {}",e.getMessage());
            throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_FETCH_FB_INSIGHTS,e.getMessage());
        }
        return responseList;
    }

    @Override
    public Integer getSumEngagementOnPageId(InsightsESRequest request) {
        try {
            if(Objects.isNull(request)) {
                log.info("request is null sending engagement as 0");
            }

            ESInsightResponse esPageInsightResponse = esService.searchInsightTemplate(request);

            Aggregations aggregations = esPageInsightResponse.getResponse().getAggregations();
            Map<String, Aggregation> aggregationsMap = aggregations.getAsMap();
            if(aggregationsMap!=null) {
                ParsedSum parsedSumEngagement = (ParsedSum) aggregationsMap.get("engagement");
                return (int) parsedSumEngagement.getValue();
            } else {
                log.info("No engagement found for pageId: {}", request.getDataModel().getPageIds());
                return 0;
            }

        } catch (Exception e) {
            log.info("Exception occurred while fetching data from es : {}",e.getMessage());
            return 0;
        }
    }

    @Override
    public Integer getSumImpressionOnPageId(InsightsESRequest request) {
        try {
            if(Objects.isNull(request)) {
                log.info("request is null sending impression as 0");
                return 0;
            }

            ESInsightResponse esPageInsightResponse = esService.searchInsightTemplate(request);

            Aggregations aggregations = esPageInsightResponse.getResponse().getAggregations();
            Map<String, Aggregation> aggregationsMap = aggregations.getAsMap();
            if(aggregationsMap!=null) {
                ParsedSum parsedSumEngagement = (ParsedSum) aggregationsMap.get("impression");
                return (int) parsedSumEngagement.getValue();
            } else {
                log.info("No impression found for pageId: {}", request.getDataModel().getPageIds());
                return 0;
            }

        } catch (Exception e) {
            log.info("Exception occurred while fetching data from es : {}",e.getMessage());
            return 0;
        }
    }

    @Override
    public Integer getSumClickCountOnPageId(InsightsESRequest request) {
        try {
            if(Objects.isNull(request)) {
                log.info("request is null sending click count as 0");
                return 0;
            }

            ESInsightResponse esPageInsightResponse = esService.searchInsightTemplate(request);

            Aggregations aggregations = esPageInsightResponse.getResponse().getAggregations();
            Map<String, Aggregation> aggregationsMap = aggregations.getAsMap();
            if(aggregationsMap!=null) {
                ParsedSum parsedSumEngagement = (ParsedSum) aggregationsMap.get("clickCount");
                return (int) parsedSumEngagement.getValue();
            } else {
                log.info("No click count found for pageId: {}", request.getDataModel().getPageIds());
                return 0;
            }

        } catch (Exception e) {
            log.info("Exception occurred while fetching click count data from es : {}",e.getMessage());
            return 0;
        }
    }

    @Override
    public PostInsightsDataPerPage getSumPostInsightsOnPageId(InsightsESRequest request) {
        PostInsightsDataPerPage postInsightsDataPerPage = new PostInsightsDataPerPage();
        try {
            if(Objects.isNull(request)) {
                log.info("Request is null sending click count as 0");
                return postInsightsDataPerPage;
            }

            ESInsightResponse esPageInsightResponse = esService.searchInsightTemplate(request);

            Aggregations aggregations = esPageInsightResponse.getResponse().getAggregations();
            Map<String, Aggregation> aggregationsMap = aggregations.getAsMap();
            if(aggregationsMap!=null) {
                ParsedSum parsedLikeCount = (ParsedSum) aggregationsMap.get("likeCount");
                ParsedSum parsedCommentCount = (ParsedSum) aggregationsMap.get("commentCount");
                ParsedSum parsedShareCount = (ParsedSum) aggregationsMap.get("shareCount");
                postInsightsDataPerPage.setLikeCount((int) parsedLikeCount.getValue());
                postInsightsDataPerPage.setCommentCount((int) parsedCommentCount.getValue());
                postInsightsDataPerPage.setShareCount((int) parsedShareCount.getValue());
                return postInsightsDataPerPage;
            } else {
                log.info("No click count found for pageId: {}", request.getDataModel().getPageIds());
                return postInsightsDataPerPage;
            }

        } catch (Exception e) {
            log.info("Exception occurred while fetching click count data from es : {}",e.getMessage());
            return postInsightsDataPerPage;
        }
    }

    @Override
    public Integer getSumVideoViewsOnPageId(InsightsESRequest request) {
        try {
            if(Objects.isNull(request)) {
                log.info("request is null sending click count as 0");
                return 0;
            }

            ESInsightResponse esPageInsightResponse = esService.searchInsightTemplate(request);

            Aggregations aggregations = esPageInsightResponse.getResponse().getAggregations();
            Map<String, Aggregation> aggregationsMap = aggregations.getAsMap();
            if(aggregationsMap!=null) {
                ParsedSum parsedSumEngagement = (ParsedSum) aggregationsMap.get("videoViews");
                return (int) parsedSumEngagement.getValue();
            } else {
                log.info("No click count found for pageId: {}", request.getDataModel().getPageIds());
                return 0;
            }

        } catch (Exception e) {
            log.info("Exception occurred while fetching click count data from es : {}",e.getMessage());
            return 0;
        }
    }


    @Override
    public PostDataAndInsightResponse getPostDataFromEs(InsightsESRequest request, boolean excelDownload) {
        PostDataAndInsightResponse insightResponse = new PostDataAndInsightResponse();
        List<PagePostData> pagePostData;
        Future<BusinessLiteDTO> getBusinessLiteFuture = null;
        getBusinessLiteFuture = executor.submit(() -> {
            if(Objects.nonNull(request.getDataModel())) {
                return businessCoreService.getBusinessLiteByNumber(request.getDataModel().getEnterpriseId());
            }
            return null;
        });
        boolean isPostedDateSortParam = request.getDataModel().getSortParam().equalsIgnoreCase("posted_date");
        boolean isPublisherNameSortParam = request.getDataModel().getSortParam().equalsIgnoreCase("publisher_name");
        if(isPublisherNameSortParam)
            request.getDataModel().setSortParam("publisher_name.keyword");
        boolean isMinutesViewedSortParam = request.getDataModel().getSortParam().equalsIgnoreCase("minutesViewed");
        if(isMinutesViewedSortParam)
            request.getDataModel().setSortParam("minutes_viewed");
        boolean isAvgMinutesViewedSortParam = request.getDataModel().getSortParam().equalsIgnoreCase("avgMinutesViewed");
        if(isAvgMinutesViewedSortParam)
            request.getDataModel().setSortParam("avg_minutes_viewed");

        if (isPostedDateSortParam || isPublisherNameSortParam || isMinutesViewedSortParam || isAvgMinutesViewedSortParam) {
            request.setSearchTemplate(getSortedSearchTemplate(request.getSearchTemplate()));
            pagePostData = performSortingOnPostedDateOrPublisherName(request, excelDownload);
        } else {
            if(excelDownload) {
                request.setSearchTemplate(getExcelDataSearchTemplate(request.getSearchTemplate()));
            }
            pagePostData = performSortingOnOtherParam(request, excelDownload);
        }
        populateLinkPreviewUrlAndReviewShareCreatedBy(pagePostData);
        BusinessLiteDTO businessLiteDTO = null;
        try {
            businessLiteDTO = getBusinessLiteFuture.get();
            filterTagsOnEnterpriseIdAndAddBasicDetailsToPostInsights(pagePostData, businessLiteDTO.getBusinessId());
        } catch (Exception e) {
            log.info("error while fetching tags, for ent id: {}, error", request.getDataModel().getEnterpriseId(),e);
        }
        insightResponse.setPagePostData(pagePostData);
        return insightResponse;
    }

    private SearchTemplate getSortedSearchTemplate(SearchTemplate searchTemplate) {
        switch (searchTemplate) {
            case POST_INSIGHTS:
                return SearchTemplate.SORT_BY_POSTED_DATE;
            case POST_INSIGHT_ALL_CHANNEL:
                return SearchTemplate.SORT_BY_POSTED_DATE_ALL;
            case POST_INSIGHTS_WITH_TAG_ID:
                return SearchTemplate.SORT_BY_POSTED_DATE_WITH_TAG_ID;
            case POST_INSIGHT_ALL_CHANNEL_WITH_TAG_ID:
                return SearchTemplate.SORT_BY_POSTED_DATE_ALL_WITH_TAG_ID ;
            case POST_INSIGHTS_WITHOUT_TAG_ID:
                return SearchTemplate.SORT_BY_POSTED_DATE_WITHOUT_TAG_ID;
            case POST_INSIGHT_ALL_CHANNEL_WITHOUT_TAG_ID:
                return SearchTemplate.SORT_BY_POSTED_DATE_ALL_WITHOUT_TAG_ID;
            case POST_INSIGHTS_WITH_TAG_ID_OR_UNTAGGED:
                return SearchTemplate.SORT_BY_POSTED_DATE_WITH_TAG_ID_OR_UNTAGGED;
            case POST_INSIGHT_ALL_CHANNEL_WITH_TAG_ID_OR_UNTAGGED:
                return SearchTemplate.SORT_BY_POSTED_DATE_ALL_WITH_TAG_ID_OR_UNTAGGED;
            default:
                return searchTemplate;
        }
    }

    private SearchTemplate getExcelDataSearchTemplate(SearchTemplate searchTemplate) {
        switch (searchTemplate) {
            case POST_INSIGHTS:
                return SearchTemplate.POST_INSIGHTS_EXCEL_DATA;
            case POST_INSIGHT_ALL_CHANNEL:
                return SearchTemplate.POST_INSIGHT_ALL_CHANNEL_EXCEL_DATA;
            case POST_INSIGHTS_WITH_TAG_ID:
                return SearchTemplate.POST_INSIGHTS_WITH_TAG_ID_EXCEL_DATA;
            case POST_INSIGHT_ALL_CHANNEL_WITH_TAG_ID:
                return SearchTemplate.POST_INSIGHT_ALL_CHANNEL_WITH_TAG_ID_EXCEL_DATA;
            case POST_INSIGHTS_WITHOUT_TAG_ID:
                return SearchTemplate.POST_INSIGHTS_WITHOUT_TAG_ID_EXCEL_DATA;
            case POST_INSIGHT_ALL_CHANNEL_WITHOUT_TAG_ID:
                return SearchTemplate.POST_INSIGHT_ALL_CHANNEL_WITHOUT_TAG_ID_EXCEL_DATA;
            case POST_INSIGHTS_WITH_TAG_ID_OR_UNTAGGED:
                return SearchTemplate.POST_INSIGHTS_WITH_TAG_ID_OR_UNTAGGED_EXCEL_DATA;
            case POST_INSIGHT_ALL_CHANNEL_WITH_TAG_ID_OR_UNTAGGED:
                return SearchTemplate.POST_INSIGHT_ALL_CHANNEL_WITH_TAG_ID_OR_UNTAGGED_EXCEL_DATA;
            default:
                return searchTemplate;
        }
    }

    private void filterTagsOnEnterpriseIdAndAddBasicDetailsToPostInsights(List<PagePostData> pagePostDataList, Integer enterpriseId) {
        Set<Long> allTagIds = new HashSet<>();
        for(PagePostData pagePostData: pagePostDataList) {
            if(SYS_ADMIN_EMAIL.equals(pagePostData.getPublisherEmail())){
                pagePostData.setPublisherEmail(null);
            }
            if(CollectionUtils.isNotEmpty(pagePostData.getTags()))
                allTagIds.addAll(pagePostData.getTags().stream().map(SocialTagBasicDetail::getId).collect(Collectors.toSet()));

        }
        if(CollectionUtils.isEmpty(allTagIds))
            return;

        Map<Long, SocialTagBasicDetail> tagIdVsBasicTagDetailsMap = socialTagService.getTagIdsBasicDetailsFilteredOnEnterpriseId(allTagIds, enterpriseId);
        for(PagePostData pagePostData: pagePostDataList) {
            if(CollectionUtils.isNotEmpty(pagePostData.getTags())) {
                pagePostData.setTags(pagePostData.getTags().stream().filter(s->tagIdVsBasicTagDetailsMap.containsKey(s.getId()))
                        .map(s->tagIdVsBasicTagDetailsMap.get(s.getId())).collect(Collectors.toList()));
            }
        }
    }

    private void populateLinkPreviewUrlAndReviewShareCreatedBy(List<PagePostData> pagePostData) {
        if(CollectionUtils.isEmpty(pagePostData)) {
            return;
        }

        List<Integer> postIds = pagePostData.stream().filter(s->s.getIsBePost())
                .map(s->Integer.parseInt(s.getBePostId())).collect(Collectors.toList());

        List<SocialPost> socialPosts = socialPostRepository.findByIdIn(postIds);
        Map<Integer, SocialPost> socialPostMap = socialPosts.stream().collect(Collectors.toMap(s->s.getId(), s->s));
        String defaultUserNameForReviewShareSaveType = CacheManager.getInstance().getCache(SystemPropertiesCache.class)
                .getProperty(SystemPropertiesCache.REVIEW_SHARE_CREATED_BY_ANALYZE_TAB);
        for(PagePostData p: pagePostData) {
            if(p.getIsBePost()) {
                Integer bePostId = Integer.parseInt(p.getBePostId());
                if(socialPostMap.containsKey(bePostId)) {
                    p.setLinkPreviewUrl(socialPostMap.get(bePostId).getLinkPreviewUrl());
                    if(Objects.nonNull(socialPostMap.get(bePostId).getAiPost())) {
                        p.setAiPost(Integer.valueOf(1).equals(socialPostMap.get(bePostId).getAiPost()));
                    }
                    // added a check to add createdBy user for review_share save type on Analyze tab.
                    if ("review_share".equals(socialPostMap.get(bePostId).getSaveType())) {
                        p.setPublisherName(defaultUserNameForReviewShareSaveType);
                    }
                }
            }
        }
    }

    private Boolean isStoryExpired(String postDate, String postType) {
        // story expiry logic
        if(StringUtils.isEmpty(postType) || !postType.equalsIgnoreCase("story")) {
            return  null;
        }
        long hourDiff = 0;
        try {
            String pattern = "yyyy-MM-dd HH:mm:ss";
            Instant instant = Instant.now();
            ZonedDateTime zonedDateTime = instant.atZone(ZoneId.of("UTC"));
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
            String formattedDate = zonedDateTime.format(formatter);
            SimpleDateFormat dateFormat = new SimpleDateFormat(pattern);
            Date currDate = dateFormat.parse(formattedDate);
            Date fromDate = dateFormat.parse(postDate);

            log.info("Curr time: {}", currDate);
            log.info("From time: {}", fromDate);

            Duration duration = Duration.between(fromDate.toInstant(), currDate.toInstant());
            hourDiff = duration.toHours();
            log.info("Diff: {}", hourDiff);
        } catch (Exception e) {
            log.info("Error occurred during hour calculation: ",e);
        }
        return hourDiff>23;
    }

    private List<PagePostData> performSortingOnPostedDateOrPublisherName(InsightsESRequest request, boolean excelDownload) {
        List<PagePostData> pagePostData = new LinkedList<>();
        try {
            ESInsightResponse esPageInsightResponse = esService.searchInsightTemplate(request, excelDownload);

            if(Objects.isNull(esPageInsightResponse)){
                throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_FETCH_FB_INSIGHTS);
            }
            SearchHit[] searchHits = esPageInsightResponse.getResponse().getHits().getHits();
            for (SearchHit searchHit : searchHits){
                EsPostDataPoint fbPostDataPointsDto = JSONUtils.fromJSON(searchHit.getSourceAsString(), EsPostDataPoint.class);
                if(Objects.isNull(fbPostDataPointsDto)){
                    return pagePostData;
                }

                List<SocialTagBasicDetail> tags = CollectionUtils.isEmpty(fbPostDataPointsDto.getTagIds())?null:
                        fbPostDataPointsDto.getTagIds().stream().map(s->new SocialTagBasicDetail(s, null))
                                .collect(Collectors.toList());

                PagePostData.PagePostDataBuilder fbPostDataPointsBuilder = PagePostData.builder()
                        .postedDate(fbPostDataPointsDto.getPosted_date())
                        .postEndDate(fbPostDataPointsDto.getPost_end_date())
                        .postContent(fbPostDataPointsDto.getPost_content())
                        .postId(fbPostDataPointsDto.getPost_id())
                        .bePostId(fbPostDataPointsDto.getBe_post_id())
                        .postContent(fbPostDataPointsDto.getPost_content())
                        .imageUrls(fbPostDataPointsDto.getImage_urls())
                        .videoUrls(fbPostDataPointsDto.getVideo_urls())
                        .pageName(fbPostDataPointsDto.getPage_name())
                        .likeCount(fbPostDataPointsDto.getLike_count())
                        .clickCount(fbPostDataPointsDto.getClick_count())
                        .shareCount(fbPostDataPointsDto.getShare_count())
                        .commentCount(fbPostDataPointsDto.getComment_count())
                        .isBePost(fbPostDataPointsDto.getIs_be_post() != null
                                ? fbPostDataPointsDto.getIs_be_post() :
                                fbPostDataPointsDto.getBe_post_id().toString()
                                        .equals(fbPostDataPointsDto.getPost_id()))
                        .postType(fbPostDataPointsDto.getPost_type())
                        .isExpired(isStoryExpired(fbPostDataPointsDto.getPosted_date(), fbPostDataPointsDto.getPost_type()))
                        .tags(tags)
                        .isDeleted(fbPostDataPointsDto.getIs_deleted())
                        .publisherId(fbPostDataPointsDto.getPublisher_id())
                        .publisherName(fbPostDataPointsDto.getPublisher_name())
                        .publisherEmail(fbPostDataPointsDto.getPublisher_email())
                        .pageId(fbPostDataPointsDto.getPage_id())
                        .businessId(fbPostDataPointsDto.getBusiness_id())
                        .videoViews(fbPostDataPointsDto.getVideo_views())
                        .sourceId(fbPostDataPointsDto.getSource_id())
                        .plays(fbPostDataPointsDto.getPlays())
                        .minutesViewed(fbPostDataPointsDto.getMinutes_viewed())
                        .avgMinutesViewed(fbPostDataPointsDto.getAvg_minutes_viewed());
                int impression = fbPostDataPointsDto.getImpression();
                if((fbPostDataPointsDto.getSource_id().equals(SocialChannel.INSTAGRAM.getId()) &&
                        Objects.nonNull(fbPostDataPointsDto.getPost_type())
                        && fbPostDataPointsDto.getPost_type().equalsIgnoreCase("reel")
                        && Objects.nonNull(fbPostDataPointsDto.getVideo_views()))){
                        impression = fbPostDataPointsDto.getVideo_views();
                }
                if (excelDownload) {
                    fbPostDataPointsBuilder
                            .impression(impression)
                            .reach(fbPostDataPointsDto.getReach())
                            .engagement(fbPostDataPointsDto.getEngagement())
                            .engagementRate(calculateEngagementRate(fbPostDataPointsDto.getEngagement(),fbPostDataPointsDto.getImpression()));
                }

                PagePostData fbPostDataPoints = fbPostDataPointsBuilder.build();

                pagePostData.add(fbPostDataPoints);
            }

            if(CollectionUtils.isEmpty(pagePostData) || excelDownload) {
                return pagePostData;
            }

            Map<String,PagePostData> pagePostDataMap = new LinkedHashMap<>();
            for(PagePostData postData : pagePostData){
                pagePostDataMap.put(postData.getBePostId(),postData);
            }
            String bePostIds = pagePostDataMap.keySet().stream().map(s -> "\"" + s + "\"").collect(Collectors.joining(","));
            InsightsEsRequestForPost insightsEsRequestForPost = null;
            if(request.getSearchTemplate() == SearchTemplate.SORT_BY_POSTED_DATE) {
                insightsEsRequestForPost = new InsightsEsRequestForPost(bePostIds,SearchTemplate.POST_AGGREGATION.getFileName(), pagePostDataMap.size(), request.getDataModel().getPageIds());
            } else {
                insightsEsRequestForPost = new InsightsEsRequestForPost(bePostIds, SearchTemplate.POST_AGGREGATION_ALL.getFileName(), pagePostDataMap.size(), request.getDataModel().getPageIds());
            }
            ESInsightResponse esPostInsightResponseAfterAggregation = esService.searchInsightTemplateAfterAggregation(insightsEsRequestForPost);
            Aggregations aggregations = esPostInsightResponseAfterAggregation.getResponse().getAggregations();
            ParsedStringTerms parsedLongTerms = aggregations.get("be_post_id");
            List<? extends Terms.Bucket> buckets =  parsedLongTerms.getBuckets();
            Map<String , ? extends Terms.Bucket> bePostIdVsBucketMap = buckets.stream().collect(Collectors.toMap(MultiBucketsAggregation.Bucket::getKeyAsString, s->s));

            for(Map.Entry<String, PagePostData> dataEntry : pagePostDataMap.entrySet() ){
                String bePostId = dataEntry.getKey();
                PagePostData postData = dataEntry.getValue();
                Terms.Bucket bucket =  bePostIdVsBucketMap.get(bePostId);
                int bePostCount = (int) bucket.getDocCount();
                Aggregations aggregatedValues = bucket.getAggregations();
                int aggregatedEngagement = 0;
                int aggregatedReach = 0;
                int aggregatedImpression = 0;
                int aggregatedLike = 0;
                int aggregatedComment = 0;
                int aggregatedShare = 0;
                Map<String, Aggregation> aggregationsMap;
                double aggregatedEngagementRate;
                List<ChannelWisePagePostData> channelWisePagePostData = null;
                if(aggregatedValues != null) {
                    aggregationsMap = aggregatedValues.getAsMap();
                    ParsedSum parsedSumEngagement = (ParsedSum) aggregationsMap.get("engagement");
                    ParsedSum parsedSumReach = (ParsedSum) aggregationsMap.get("reach");
                    ParsedSum parsedSumImpression = (ParsedSum) aggregationsMap.get("impression");
                    if(Objects.nonNull(postData) && postData.getSourceId().equals(SocialChannel.INSTAGRAM.getId()) && Objects.nonNull(postData.getPostType()) && postData.getPostType().equalsIgnoreCase("reel")
                        && aggregationsMap.containsKey("video_views") && Objects.nonNull(aggregationsMap.get("video_views"))) {
                            parsedSumImpression = (ParsedSum) aggregationsMap.get("video_views");
                    }
                    ParsedSum parsedSumLike = (ParsedSum) aggregationsMap.get("likeCount");
                    ParsedSum parsedSumComment = (ParsedSum) aggregationsMap.get("commentCount");
                    ParsedSum parsedSumShare = (ParsedSum) aggregationsMap.get("shareCount");
                    ParsedLongTerms sourceIdParsedLongTerm = aggregatedValues.get("source_id");
                    if(sourceIdParsedLongTerm != null) {
                        channelWisePagePostData = getChannelWiseInsights(sourceIdParsedLongTerm, postData.getPostType(), postData.getSourceId());
                    }
                    if(parsedSumEngagement !=null) {
                        aggregatedEngagement = (int)parsedSumEngagement.getValue();
                    }
                    if(parsedSumReach !=null) {
                        aggregatedReach = (int)parsedSumReach.getValue();
                    }
                    if(parsedSumImpression !=null) {
                        aggregatedImpression = (int)parsedSumImpression.getValue();
                    }
                    if(parsedSumLike !=null) {
                        aggregatedLike = (int)parsedSumLike.getValue();
                    }
                    if(parsedSumComment !=null) {
                        aggregatedComment = (int)parsedSumComment.getValue();
                    }
                    if(parsedSumShare !=null) {
                        aggregatedShare = (int)parsedSumShare.getValue();
                    }
                }
                aggregatedEngagementRate = calculateEngagementRate(aggregatedEngagement, aggregatedImpression);
                postData.setImpression(aggregatedImpression);
                postData.setEngagement(aggregatedEngagement);
                postData.setReach(aggregatedReach);
                postData.setLikeCount(aggregatedLike);
                postData.setCommentCount(aggregatedComment);
                postData.setShareCount(aggregatedShare);
                postData.setBePostPageCount(bePostCount);
                postData.setEngagementRate(aggregatedEngagementRate);
                postData.setChannelWisePostInsights(channelWisePagePostData);
            }

            return new LinkedList<>(pagePostDataMap.values());
        } catch (Exception e) {
            log.error("Exception for getting the post insights data from elastic search for request: {} and error:{} ", request, e);
        }
        return pagePostData;
    }
//    private String convertDateIntoSpecificTimeZone(String dateString, String timezone) throws ParseException {
//        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(dateFormatterString, Locale.ENGLISH);
//        Date date = simpleDateFormat.parse(dateString);
//        return TimeZoneUtil.convertToSpecificTimeZone(date, timezone).getTime().toString();
//    }

    private List<ChannelWisePagePostData> getChannelWiseInsights(ParsedLongTerms parsedLongTerms, String postType, Integer sourceId) {
        List<ChannelWisePagePostData> channelWisePagePostDataList = new ArrayList<>();
        List<? extends Terms.Bucket> buckets =  parsedLongTerms.getBuckets();

        for(Terms.Bucket bucket: buckets) {
            Integer key = bucket.getKeyAsNumber().intValue();
            Aggregations aggregatedValues = bucket.getAggregations();
            int aggregatedEngagement = 0;
            int aggregatedReach = 0;
            int aggregatedImpression = 0;
            int aggregatedLike = 0;
            int aggregatedComment = 0;
            int aggregatedShare = 0;
            int aggregatedPlays = 0;
            int aggregatedMinutesViewed = 0;
            int aggregatedAvgMinutesViewed = 0;
            Map<String, Aggregation> aggregationsMap;
            double aggregatedEngagementRate;
            if(aggregatedValues != null) {
                aggregationsMap = aggregatedValues.getAsMap();
                ParsedSum parsedSumEngagement = (ParsedSum) aggregationsMap.get("engagement");
                ParsedSum parsedSumReach = (ParsedSum) aggregationsMap.get("reach");
                ParsedSum parsedSumImpression = (ParsedSum) aggregationsMap.get("impression");
                if(SocialChannel.INSTAGRAM.equals(SocialChannel.getSocialChannelById(sourceId)) && Objects.nonNull(postType)
                        && "reel".equalsIgnoreCase(postType)) {
                    parsedSumImpression = (ParsedSum) aggregationsMap.get("video_views");
                }
                ParsedSum parsedSumLike = (ParsedSum) aggregationsMap.get("likeCount");
                ParsedSum parsedSumComment = (ParsedSum) aggregationsMap.get("commentCount");
                ParsedSum parsedSumShare = (ParsedSum) aggregationsMap.get("shareCount");
                ParsedSum parsedSumPlays = (ParsedSum) aggregationsMap.get("plays");
                ParsedSum parsedSumMinutesViewed = (ParsedSum) aggregationsMap.get("minutesViewed");
                ParsedSum parsedSumAvgMinutesViewed = (ParsedSum) aggregationsMap.get("avgMinutesViewed");
                if(parsedSumEngagement !=null) {
                    aggregatedEngagement = (int)parsedSumEngagement.getValue();
                }
                if(parsedSumReach !=null) {
                    aggregatedReach = (int)parsedSumReach.getValue();
                }
                if(parsedSumImpression !=null) {
                    aggregatedImpression = (int)parsedSumImpression.getValue();
                }
                if(parsedSumLike !=null) {
                    aggregatedLike = (int)parsedSumLike.getValue();
                }
                if(parsedSumComment !=null) {
                    aggregatedComment = (int)parsedSumComment.getValue();
                }
                if(parsedSumShare !=null) {
                    aggregatedShare = (int)parsedSumShare.getValue();
                }
                if(parsedSumPlays !=null) {
                    aggregatedPlays = (int)parsedSumPlays.getValue();
                }
                if(parsedSumMinutesViewed !=null) {
                    aggregatedMinutesViewed = (int)parsedSumMinutesViewed.getValue();
                }
                if(parsedSumAvgMinutesViewed !=null) {
                    aggregatedAvgMinutesViewed = (int)parsedSumAvgMinutesViewed.getValue();
                }
            }

            aggregatedEngagementRate = calculateEngagementRate(aggregatedEngagement, aggregatedImpression);
            ChannelWisePagePostData channelWisePagePostData = ChannelWisePagePostData.builder()
                    .channelName(SocialChannel.getSocialChannelNameById(key))
                    .engagement(aggregatedEngagement)
                    .shareCount(aggregatedShare)
                    .commentCount(aggregatedComment)
                    .likeCount(aggregatedLike)
                    .impression(aggregatedImpression)
                    .reach(aggregatedReach)
                    .engagementRate(aggregatedEngagementRate)
                    .plays(aggregatedPlays)
                    .minutesViewed(aggregatedMinutesViewed)
                    .avgMinutesViewed(aggregatedAvgMinutesViewed)
                    .build();

            channelWisePagePostDataList.add(channelWisePagePostData);
        }

        return channelWisePagePostDataList;
    }

    private List<PagePostData> performSortingOnOtherParam(InsightsESRequest request, boolean excelDownload) {
        List<PagePostData> pagePostData = new LinkedList<>();
        try {
            ESInsightResponse esPageInsightResponse = esService.searchInsightTemplate(request);

            if(Objects.isNull(esPageInsightResponse)){
                throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_FETCH_FB_INSIGHTS);
            }
            if(excelDownload) {
                SearchHit[] searchHits = esPageInsightResponse.getResponse().getHits().getHits();
                for(SearchHit searchHit: searchHits) {
                    EsPostDataPoint fbPostDataPointsDto = JSONUtils.fromJSON(searchHit.getSourceAsString(), EsPostDataPoint.class);
                    if(Objects.isNull(fbPostDataPointsDto)){
                        return pagePostData;
                    }
                    List<SocialTagBasicDetail> tags = CollectionUtils.isEmpty(fbPostDataPointsDto.getTagIds())?null:
                            fbPostDataPointsDto.getTagIds().stream().map(s->new SocialTagBasicDetail(s, null))
                                    .collect(Collectors.toList());
                    PagePostData pagePostData1 = buildPagePostData(fbPostDataPointsDto, fbPostDataPointsDto.getEngagement_rate(), fbPostDataPointsDto.getReach(), fbPostDataPointsDto.getEngagement(),
                            fbPostDataPointsDto.getImpression(), 0, new ArrayList<>(), tags);
                    pagePostData.add(pagePostData1);
                }
                return pagePostData;
            }
            Aggregations aggregations = esPageInsightResponse.getResponse().getAggregations();
            ParsedStringTerms parsedLongTerms = aggregations.get("be_post_id");
            List<? extends Terms.Bucket> buckets =  parsedLongTerms.getBuckets().subList(Integer.parseInt(request.getDataModel().getFrom()),parsedLongTerms.getBuckets().size());
            Map<String, Terms.Bucket> bePostIdVsBucketMap = new LinkedHashMap<>();
            Map<String,PagePostData> pagePostDataMap = new LinkedHashMap<>();
            for (Terms.Bucket bucket : buckets){
                bePostIdVsBucketMap.put(bucket.getKeyAsString(),bucket);
            }
            List<String> bePostIdList = buckets.stream().map(MultiBucketsAggregation.Bucket::getKeyAsString).collect(Collectors.toCollection(LinkedList::new));
            if(CollectionUtils.isEmpty(bePostIdList)) {
                return pagePostData;
            }
            String bePostIds = bePostIdList.stream().map(s -> "\"" + s + "\"").collect(Collectors.joining(","));

            InsightsEsRequestForPost insightsEsRequestForPost = new InsightsEsRequestForPost(bePostIds,"post_insights_after_aggregation.ftl", bePostIdList.size(), request.getDataModel().getPageIds());
            ESInsightResponse esPostInsightResponseAfterAggregation = esService.searchInsightTemplateAfterAggregation(insightsEsRequestForPost);

            SearchResponse searchResponse = esPostInsightResponseAfterAggregation.getResponse();

            SearchHit[] searchHits = searchResponse.getHits().getHits();
            for(SearchHit searchHit: searchHits) {
                try {
                    EsPostDataPoint fbPostDataPointsDto = JSONUtils.fromJSON(searchHit.getSourceAsString(), EsPostDataPoint.class);
                    if(Objects.isNull(fbPostDataPointsDto)){
                        return pagePostData;
                    }
                    Terms.Bucket bucket = bePostIdVsBucketMap.get(fbPostDataPointsDto.getBe_post_id());
                    int bePostCount = (int) bucket.getDocCount();
                    Aggregations aggregatedValues = bucket.getAggregations();
                    Map<String, Aggregation> aggregationsMap = new LinkedHashMap<>();
                    int aggregatedEngagement = 0;
                    int aggregatedReach = 0;
                    int aggregatedImpression = 0;
                    int aggregatedLike = 0;
                    int aggregatedComment = 0;
                    int aggregatedShare = 0;
                    double aggregatedEngagementRate;
                    List<ChannelWisePagePostData> channelWisePagePostData = null;
                    if(aggregatedValues != null) {
                        aggregationsMap = aggregatedValues.getAsMap();
                        ParsedSum parsedSumEngagement = (ParsedSum) aggregationsMap.get("engagement");
                        ParsedSum parsedSumReach = (ParsedSum) aggregationsMap.get("reach");
                        ParsedSum parsedSumImpression = (ParsedSum) aggregationsMap.get("impression");
                        if(SocialChannel.INSTAGRAM.equals(SocialChannel.getSocialChannelById(fbPostDataPointsDto.getSource_id()))
                                && Objects.nonNull(fbPostDataPointsDto.getPost_type())
                                && fbPostDataPointsDto.getPost_type().equalsIgnoreCase("reel")
                                && aggregationsMap.containsKey("video_views")
                                && Objects.nonNull(aggregationsMap.get("video_views"))) {
                            parsedSumImpression = (ParsedSum) aggregationsMap.get("video_views");
                        }
                        ParsedSum parsedSumLike = (ParsedSum) aggregationsMap.get("likeCount");
                        ParsedSum parsedSumComment = (ParsedSum) aggregationsMap.get("commentCount");
                        ParsedSum parsedSumShare = (ParsedSum) aggregationsMap.get("shareCount");
                        ParsedLongTerms sourceIdParsedLongTerm = aggregatedValues.get("source_id");
                        if(sourceIdParsedLongTerm != null) {
                            channelWisePagePostData = getChannelWiseInsights(sourceIdParsedLongTerm, fbPostDataPointsDto.getPost_type(), fbPostDataPointsDto.getSource_id());
                        }
                        if(parsedSumEngagement !=null) {
                            aggregatedEngagement = (int)parsedSumEngagement.getValue();
                        }
                        if(parsedSumReach !=null) {
                            aggregatedReach = (int)parsedSumReach.getValue();
                        }
                        if(parsedSumImpression !=null) {
                            aggregatedImpression = (int)parsedSumImpression.getValue();
                        }
                        if(parsedSumLike !=null) {
                            aggregatedLike = (int)parsedSumLike.getValue();
                        }
                        if(parsedSumComment !=null) {
                            aggregatedComment = (int)parsedSumComment.getValue();
                        }
                        if(parsedSumShare !=null) {
                            aggregatedShare = (int)parsedSumShare.getValue();
                        }
                    }
                    aggregatedEngagementRate = calculateEngagementRate(aggregatedEngagement, aggregatedImpression);
                    List<SocialTagBasicDetail> tags = CollectionUtils.isEmpty(fbPostDataPointsDto.getTagIds())?null:
                            fbPostDataPointsDto.getTagIds().stream().map(s->new SocialTagBasicDetail(s, null))
                                    .collect(Collectors.toList());
                    PagePostData fbPostDataPoints = buildPagePostData(fbPostDataPointsDto, aggregatedEngagementRate, aggregatedReach, aggregatedEngagement,
                            aggregatedImpression, bePostCount, channelWisePagePostData, tags);
                    fbPostDataPoints.setCommentCount(aggregatedComment);
                    fbPostDataPoints.setLikeCount(aggregatedLike);
                    fbPostDataPoints.setShareCount(aggregatedShare);
                    pagePostDataMap.put(fbPostDataPoints.getBePostId(),fbPostDataPoints);
                } catch (Exception e) {
                    log.info("error in getting fb data point for query: {} with error: {}",searchHit, e.getMessage());
                }
            }
            List<PagePostData> sortedValues = bePostIdList.stream().filter(pagePostDataMap::containsKey).map(pagePostDataMap::get)
                    .collect(Collectors.toCollection(LinkedList::new));
            pagePostData.addAll(sortedValues);
        } catch (Exception e) {
            log.error("Exception for getting the post insights data from elastic search for request: {} and error", request, e);
        }
        return pagePostData;
    }

    private PagePostData buildPagePostData(EsPostDataPoint fbPostDataPointsDto, double aggregatedEngagementRate,
                                           int aggregatedReach, int aggregatedEngagement, int aggregatedImpression,
                                           int bePostCount, List<ChannelWisePagePostData> channelWisePagePostData,
                                           List<SocialTagBasicDetail> tags) {
        return PagePostData.builder().
                postedDate(fbPostDataPointsDto.getPosted_date())
                .postEndDate(fbPostDataPointsDto.getPost_end_date())
                .engagementRate(calculateEngagementRate(fbPostDataPointsDto.getEngagement(),fbPostDataPointsDto.getImpression()))
                .postContent(fbPostDataPointsDto.getPost_content())
                .postId(fbPostDataPointsDto.getPost_id())
                .bePostId(fbPostDataPointsDto.getBe_post_id())
                .reach(aggregatedReach)
                .engagement(aggregatedEngagement)
                .impression(aggregatedImpression)
                .pageName(fbPostDataPointsDto.getPage_name())
                .postContent(fbPostDataPointsDto.getPost_content())
                .imageUrls(fbPostDataPointsDto.getImage_urls())
                .videoUrls(fbPostDataPointsDto.getVideo_urls())
                .bePostPageCount(bePostCount)
                .likeCount(fbPostDataPointsDto.getLike_count())
                .clickCount(fbPostDataPointsDto.getClick_count())
                .shareCount(fbPostDataPointsDto.getShare_count())
                .commentCount(fbPostDataPointsDto.getComment_count())
                .isBePost(fbPostDataPointsDto.getIs_be_post()!=null
                        ?fbPostDataPointsDto.getIs_be_post():
                        fbPostDataPointsDto.getBe_post_id().toString()
                                .equals(fbPostDataPointsDto.getPost_id()))
                .channelWisePostInsights(channelWisePagePostData)
                .postType(fbPostDataPointsDto.getPost_type())
                .isExpired(isStoryExpired(fbPostDataPointsDto.getPosted_date(),fbPostDataPointsDto.getPost_type()))
                .publisherId(fbPostDataPointsDto.getPublisher_id())
                .publisherName(fbPostDataPointsDto.getPublisher_name())
                .publisherEmail(fbPostDataPointsDto.getPublisher_email())
                .pageId(fbPostDataPointsDto.getPage_id())
                .businessId(fbPostDataPointsDto.getBusiness_id())
                .sourceId(fbPostDataPointsDto.getSource_id())
                .tags(tags)
                .isDeleted(fbPostDataPointsDto.getIs_deleted())
                .plays(fbPostDataPointsDto.getPlays())
                .minutesViewed(fbPostDataPointsDto.getMinutes_viewed())
                .avgMinutesViewed(fbPostDataPointsDto.getAvg_minutes_viewed())
                .build();
    }

    public  Double calculateEngagementRate(Integer aggregatedEngagement, Integer aggregatedImpression) {
        Double aggregatedEngagementRate = 0d;
        if(aggregatedImpression != 0 ) {
            aggregatedEngagementRate =  ((double)aggregatedEngagement/(double)aggregatedImpression)*100d;
        }
        return Double.valueOf(new DecimalFormat("#.#").format(aggregatedEngagementRate));
    }

    @Override
    public List<ESPageRequest> getPageDataForPageId(String pageId, Integer businessId, Date date, String index) throws IOException {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        if(Objects.nonNull(businessId)) {
            TermQueryBuilder businessIdTerms = new TermQueryBuilder("business_id", businessId);
            boolQueryBuilder.must(businessIdTerms);
        }
        TermQueryBuilder pageIdTerms = new TermQueryBuilder("page_id",pageId);
        SimpleDateFormat dateFormatter= new SimpleDateFormat(dateFormatterString);
        if(Objects.nonNull(date)) {
            RangeQueryBuilder rangeQueryBuilder = new RangeQueryBuilder("day");
            rangeQueryBuilder.gte(dateFormatter.format(DateUtils.truncate(date, Calendar.DATE)));
            rangeQueryBuilder.lte(dateFormatter.format(DateUtils.addMilliseconds(DateUtils.ceiling(date, Calendar.DATE), -1)));
            boolQueryBuilder.must(rangeQueryBuilder);
        }
        boolQueryBuilder.must(pageIdTerms);

        return getData(boolQueryBuilder, index,1000);
    }

    @Override
    public List<EsPostDataPoint> getPostDataForPageId(String pageId, Integer businessId,String index,Integer sourceId) throws IOException {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        if(Objects.nonNull(businessId)) {
            TermQueryBuilder businessIdTerms = new TermQueryBuilder("business_id", businessId);
            boolQueryBuilder.must(businessIdTerms);
        }
        TermQueryBuilder sourceIdTerm = new TermQueryBuilder("source_id", sourceId);
        boolQueryBuilder.must(sourceIdTerm);
        TermQueryBuilder pageIdTerms = new TermQueryBuilder("page_id",pageId);
        boolQueryBuilder.must(pageIdTerms);

        return getPostData(boolQueryBuilder, index);
    }

    private List<EsPostDataPoint> getPostData(AbstractQueryBuilder boolQueryBuilder, String index) throws IOException {
        List<EsPostDataPoint> esPostDataPoints = new ArrayList<>();
        SearchRequest searchRequest = new SearchRequest();
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(boolQueryBuilder);
        searchSourceBuilder.size(1000);
        searchRequest.scroll(TimeValue.timeValueSeconds(30));
        searchRequest.source(searchSourceBuilder);
        searchRequest.indices(index);
        SearchResponse searchResponse = esCommonService.search(searchRequest);
        SearchHit[] searchHits =  searchResponse.getHits().getHits();
        if(searchHits.length > 0) {
            for (SearchHit searchHit : searchHits) {
                EsPostDataPoint esPageRequest = new ObjectMapper().disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES).readValue(searchHit.getSourceAsString(), EsPostDataPoint.class);
                esPostDataPoints.add(esPageRequest);
            }
        }
        return esPostDataPoints;
    }

    @Override
    public void updatePostCountForDates(InsightsESRequest request, PageInsightEsData pageInsightEsData, List<String> pageIds, Date queryStartDate) throws IOException {
        if(!SearchTemplate.PAGE_POST_ENGAGEMENT.equals(request.getSearchTemplate())) {
            return;
        }
        request.setSearchTemplate(SearchTemplate.POST_INSIGHT_POST_COUNT);
        request.setIndex(ElasticConstants.POST_INSIGHTS.getName());

        try {
            ESInsightResponse esPageInsightResponse = esService.searchInsightTemplate(request);

            Aggregations aggregations = esPageInsightResponse.getResponse().getAggregations();
            if(Objects.isNull(aggregations)){
                return;
            }

            ParsedFilter prevDataAgg = aggregations.get(PREV_DATA);
            ParsedFilter currentDataAgg = aggregations.get(CURRENT_DATA);
            ParsedFilter data = aggregations.get(DATA);
            if(Objects.nonNull(prevDataAgg) && Objects.nonNull(pageInsightEsData.getPrevData()) && Objects.nonNull(prevDataAgg.getAggregations().get("post_count"))) {
                ParsedValueCount valueCount = prevDataAgg.getAggregations().get("post_count");
                pageInsightEsData.getPrevData().setTotalPost((int) valueCount.getValue());
            }
            if(Objects.nonNull(currentDataAgg) && Objects.nonNull(currentDataAgg.getAggregations().get("post_count"))) {
                ParsedValueCount valueCount = currentDataAgg.getAggregations().get("post_count");
                pageInsightEsData.getCurrentData().setTotalPost((int) valueCount.getValue());
            }

            ParsedDateHistogram dateHistogramAggregation = data.getAggregations().get(HISTOGRAM);
            if (Objects.isNull(dateHistogramAggregation) || Objects.isNull(dateHistogramAggregation.getBuckets())) {
                return;
            }
            List<? extends Histogram.Bucket> buckets = dateHistogramAggregation.getBuckets();

            Map<String, Integer> postCountMap = createPostCountDateWiseMap(buckets);

            pageInsightEsData.getPointsList().forEach(pageInsightDataPoint -> {
                if(postCountMap.containsKey(pageInsightDataPoint.getLabel())) {
                    pageInsightDataPoint.setTotalPosts(postCountMap.get(pageInsightDataPoint.getLabel()));
                    postCountMap.remove(pageInsightDataPoint.getLabel());
                } else {
                    pageInsightDataPoint.setTotalPosts(0);
                }
            });

            postCountMap.forEach((date, count) -> {
                PageInsightDataPoint dataPoint = new PageInsightDataPoint();
                dataPoint.setLabel(date);
                dataPoint.setTotalPosts(count);

                pageInsightEsData.getPointsList().add(dataPoint);
            });

        } catch (Exception e){
            log.info("Exception occurred while fetching data from es : {}",e.getMessage());
            throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_FETCH_FB_INSIGHTS,e.getMessage());
        }
    }
    @Override
    public void updatePostCountForDates(InsightsESRequest request, PageInsightV2EsData pageInsightEsData, List<String> pageIds, Date queryStartDate) throws IOException {
        if(!(SearchTemplate.PAGE_POST_ENGAGEMENT.equals(request.getSearchTemplate()) || SearchTemplate.PPR_PAGE_POST_PUBLISHED_METRIC.equals(request.getSearchTemplate()))) {
            return;
        }
        request.setSearchTemplate(SearchTemplate.POST_INSIGHT_POST_COUNT);
        request.setIndex(ElasticConstants.POST_INSIGHTS.getName());

        try {
            ESInsightResponse esPageInsightResponse = esService.searchInsightTemplate(request);

            Aggregations aggregations = esPageInsightResponse.getResponse().getAggregations();
            if(Objects.isNull(aggregations)){
                return;
            }

            ParsedFilter prevDataAgg = aggregations.get(PREV_DATA);
            ParsedFilter currentDataAgg = aggregations.get(CURRENT_DATA);
            ParsedFilter data = aggregations.get(DATA);
            if(Objects.nonNull(prevDataAgg) && Objects.nonNull(pageInsightEsData.getPrevData()) && Objects.nonNull(prevDataAgg.getAggregations().get("post_count"))) {
                ParsedValueCount valueCount = prevDataAgg.getAggregations().get("post_count");
                pageInsightEsData.getPrevData().setPostCount((int) valueCount.getValue());
            }
            if(Objects.nonNull(currentDataAgg) && Objects.nonNull(currentDataAgg.getAggregations().get("post_count"))) {
                ParsedValueCount valueCount = currentDataAgg.getAggregations().get("post_count");
                pageInsightEsData.getCurrentData().setPostCount((int) valueCount.getValue());
            }

            ParsedDateHistogram dateHistogramAggregation = data.getAggregations().get(HISTOGRAM);
            if (Objects.isNull(dateHistogramAggregation) || Objects.isNull(dateHistogramAggregation.getBuckets())) {
                return;
            }
            List<? extends Histogram.Bucket> buckets = dateHistogramAggregation.getBuckets();

            Map<String, Integer> postCountMap = createPostCountDateWiseMap(buckets);

            pageInsightEsData.getPointsList().forEach(pageInsightDataPoint -> {
                if(postCountMap.containsKey(pageInsightDataPoint.getLabel())) {
                    pageInsightDataPoint.setTotalPosts(postCountMap.get(pageInsightDataPoint.getLabel()));
                    postCountMap.remove(pageInsightDataPoint.getLabel());
                } else {
                    pageInsightDataPoint.setTotalPosts(0);
                }
            });

            postCountMap.forEach((date, count) -> {
                PageInsightDataPoint dataPoint = new PageInsightDataPoint();
                dataPoint.setLabel(date);
                dataPoint.setTotalPosts(count);

                pageInsightEsData.getPointsList().add(dataPoint);
            });

        } catch (Exception e){
            log.info("Exception occurred while fetching data from es : {}",e.getMessage());
            throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_FETCH_FB_INSIGHTS,e.getMessage());
        }
    }

    @Override
    public Date getFirstPostDate(InsightsRequest request, List<String> pageIds, Date queryStartDate) throws IOException {
        SimpleDateFormat dateFormatter = new SimpleDateFormat(dateFormatterString);
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        TermsQueryBuilder businessIdsTerms = new TermsQueryBuilder("page_id", pageIds);
        boolQueryBuilder.must(businessIdsTerms);

        Date pageInsightStartDate = request.getStartDate();
        String postInsightStartDate = getStartDateForPostInsight(boolQueryBuilder, ElasticConstants.POST_INSIGHTS.getName(), 10);

        try {
            String earlierDate = pageInsightStartDate.before(dateFormatter.parse(postInsightStartDate)) ?
                    dateFormatter.format(pageInsightStartDate) : postInsightStartDate;
            return dateFormatter.parse(earlierDate).before(queryStartDate) ? queryStartDate : dateFormatter.parse(earlierDate);
        } catch (ParseException e) {
            log.info("Exception occurred while parsing startDate from es: {}", postInsightStartDate);
            throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_FETCH_FB_INSIGHTS,e.getMessage());
        }
    }

    @Override
    public void bulkPageInsightsPostCountUpdate(List<ESPageRequest> esPageRequest, String index) {
        try {
            BulkRequest bulkRequest = prepareBulkRequestForPageInsightsPostCount(esPageRequest,index);
            esCommonService.addBulkDocument(bulkRequest);
        }catch (IOException e){
            log.error("IOException occurred while posting post data and insights to elastic search error: {}", e.getMessage());
        }
    }



    @Override
    public List<PageLevelMetaData> mergeAndGetUpdatePageInsightList(List<PageLevelMetaData> pageLevelMetaDataList, Map<Date, Integer> postCountMap) {
        SimpleDateFormat dateFormatter= new SimpleDateFormat(dateFormatterString);
        pageLevelMetaDataList.forEach(pageLevelMetaData -> {
            try {
                Date date = dateFormatter.parse(getDateStringWithZeroTime(dateFormatter.format(pageLevelMetaData.getDate())));
                if(postCountMap.containsKey(date)) {
                    pageLevelMetaData.setPostCount(postCountMap.get(date));
                    postCountMap.remove(date);
                }
            } catch (ParseException e) {
                throw new RuntimeException(e);
            }
        });

        return getPostCountUpdateList(postCountMap);
    }

    @Override
    public void updatePageInsightsPostCount(PageInsights pageInsights, String index) {
        log.info("Started es post count update for business id : {}", pageInsights.getBusinessId());
        List<ESPageRequest> esPageRequest = reportDataConverter.createPageInsightsObject(pageInsights);
        bulkPageInsightsPostCountUpdate(esPageRequest, index);
        log.info("Updated es post count for business id :{}",pageInsights.getBusinessId());
    }

    @Override
    public String getStartDate(List<String> pageIds,String index) throws IOException {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        TermsQueryBuilder businessIdsTerms = new TermsQueryBuilder("page_id",pageIds);
        boolQueryBuilder.must(businessIdsTerms);
        List<ESPageRequest> esPageDataPoints = getData(boolQueryBuilder, index,10);
        if(CollectionUtils.isNotEmpty(esPageDataPoints)) {
            return esPageDataPoints.get(0).getDay();
        }
        return new SimpleDateFormat(dateFormatterString).format(new Date());
    }

    @Override
    public long getTimeDifference(InsightsESRequest request) {
        SimpleDateFormat dateFormatter = new SimpleDateFormat(dateFormatterString);

        long diff = 0;
        try {
            diff = dateFormatter.parse(request.getDataModel().getEndDate()).getTime() -
                    dateFormatter.parse(request.getDataModel().getStartDate()).getTime();

            long oneDayInMillis = 86400000l;
            long re = diff % oneDayInMillis;
            if(oneDayInMillis - re == 1000l) {
                diff+=1000l;
            }
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        return TimeUnit.DAYS.convert(diff, TimeUnit.MILLISECONDS);
    }


    @Override
    public long getFeedTimeDifference(InsightsESRequest request) {
        SimpleDateFormat df = new SimpleDateFormat(feedDateFormatterString);

        long diff = 0;
        try {
            diff = df.parse(request.getDataModel().getEndDate()).getTime() -
                    df.parse(request.getDataModel().getStartDate()).getTime();
            long oneDayInMillis = 86400000l;
            long re = diff % oneDayInMillis;
            if(oneDayInMillis - re == 1000l) {
                diff+=1000l;
            }
        } catch (ParseException ex) {
            throw new RuntimeException(ex);
        }
        return TimeUnit.DAYS.convert(diff, TimeUnit.MILLISECONDS);
    }

    @Override
    public PageInsightsV2Response createResponseForPageInsight(Map<String, PageInsightV2EsData> map, InsightsRequest insightsRequest, int bucketSize) {
        log.info("Fetched insight from es for pages");
        PageInsightsV2Response pageInsightsResponse = null;
        try {
            pageInsightsResponse = reportDataConverter.prepareCommonInsightResponse(map, insightsRequest, bucketSize);
        } catch (ParseException e) {
            throw new SocialBirdeyeException(ErrorCodes.UNKNOWN_ERROR_OCCURRED, e.getMessage());
        }
        return pageInsightsResponse;
    }

    private List<PageLevelMetaData> getPostCountUpdateList(Map<Date, Integer> postCountMap) {
        List<PageLevelMetaData> postCountUpdateList = new ArrayList<>();

        postCountMap.forEach((date, postCount) -> {
            PageLevelMetaData data = new PageLevelMetaData();
            data.setDate(date);
            data.setPostCount(postCount);

            postCountUpdateList.add(data);
        });

        return postCountUpdateList;
    }

    private Map<String, Integer> createPostCountDateWiseMap(List<? extends Histogram.Bucket> buckets) throws ParseException {
        Map<String, Integer> response = new HashMap<>();
        SimpleDateFormat dateFormatter= new SimpleDateFormat(dateFormatterString);
        SimpleDateFormat usDateFormatter = new SimpleDateFormat(usDateFormatString);
        buckets.forEach(bucket -> {
            try {
                ParsedValueCount valueCount = bucket.getAggregations().get("post_count");
                response.put(usDateFormatter.format(dateFormatter.parse(bucket.getKeyAsString())), (int) valueCount.getValue());
            } catch (ParseException e) {
                throw new RuntimeException(e);
            }
        });

        return response;
    }

    private List<ESPageRequest> getData(AbstractQueryBuilder boolQueryBuilder,String index,Integer size) throws IOException {
        List<ESPageRequest> esPageRequests = new ArrayList<>();
        SearchRequest searchRequest = new SearchRequest();
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(boolQueryBuilder);
        searchSourceBuilder.size(size);
        if(size == 10) {
            searchSourceBuilder.sort("day", SortOrder.ASC);
        }
        searchRequest.scroll(TimeValue.timeValueSeconds(30));
        searchRequest.source(searchSourceBuilder);
        searchRequest.indices(index);
        SearchResponse searchResponse = esCommonService.search(searchRequest);
        SearchHit[] searchHits =  searchResponse.getHits().getHits();
        if(searchHits.length > 0) {
            for (SearchHit searchHit : searchHits) {
                ESPageRequest esPageRequest = new ObjectMapper().disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES).readValue(searchHit.getSourceAsString(), ESPageRequest.class);
                esPageRequests.add(esPageRequest);
            }
        }
        return esPageRequests;
    }


    public String getStartDateForPostInsight(AbstractQueryBuilder boolQueryBuilder,String index,Integer size) throws IOException {
        SimpleDateFormat dateFormatter = new SimpleDateFormat(dateFormatterString);
        SearchRequest searchRequest = new SearchRequest();
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(boolQueryBuilder);
        searchSourceBuilder.size(size);
        searchSourceBuilder.sort("posted_date", SortOrder.ASC);
        searchRequest.scroll(TimeValue.timeValueSeconds(30));
        searchRequest.source(searchSourceBuilder);
        searchRequest.indices(index);
        SearchResponse searchResponse = esCommonService.search(searchRequest);
        SearchHit[] searchHits =  searchResponse.getHits().getHits();
        if(searchHits.length > 0) {
                Object postInsight =
                        new ObjectMapper().disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES).readValue(searchHits[0].getSourceAsString(), Object.class);
                return ((LinkedHashMap) postInsight).get("posted_date").toString();
        }
        return dateFormatter.format(new Date());
    }

    @Override
    public void bulkPostPageInsights(List<ESPageRequest> esPageRequests, String index) {
        try {
            if(CollectionUtils.isEmpty(esPageRequests)) {
                log.info("empty payload for bulk update in es for index: {}", index);
                return ;
            }
            BulkRequest bulkRequest = prepareBulkRequestForPage(esPageRequests,index);
            esCommonService.addBulkDocument(bulkRequest);
        }catch (IOException e){
            log.error("IOException occurred while posting post data and insights to elastic search error: {}", e.getMessage());
        }
    }

    @Override
    public void bulkPostPagePostDataToES(List<EsPostDataPoint> esPostDataPoints, String index) {
        try {
            BulkRequest bulkRequest = prepareBulkRequestForPost(esPostDataPoints,index);
            log.info("Converted to es bulk request for page id :{}",esPostDataPoints.get(0).getPage_id());
            esCommonService.addBulkDocument(bulkRequest);
            log.info("ES upload successful!!");
        }catch (IOException e){
            log.error("IOException occurred while posting page insights data to elastic search error: {}", e.getMessage());
        }
    }
    @Override
    public Map<String, CalendarViewPagePostInsightsData> getCalendarViewPagePostDataFromEs(InsightsEsRequestForCalendarPage request) {
        try {
            ESInsightResponse esPageInsightResponse = esService.searchInsightTemplateForCalendarViewPage(request);

            if(Objects.isNull(esPageInsightResponse)){
                throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_FETCH_FB_INSIGHTS);
            }
            Aggregations aggregations = esPageInsightResponse.getResponse().getAggregations();

            ParsedLongTerms parsedLongTerms = aggregations.get("source_id_aggregation");
            List<? extends Terms.Bucket> buckets =  parsedLongTerms.getBuckets();
            Map<Integer, ? extends Terms.Bucket> sourceIdVsBucketMap = buckets.stream().collect(Collectors.toMap(s->s.getKeyAsNumber().intValue(), s->s));

            Map<String, CalendarViewPagePostInsightsData> responseMap = new HashMap<>();
            for(Map.Entry<Integer, ? extends Terms.Bucket> entry: sourceIdVsBucketMap.entrySet()) {
                Integer sourceId = entry.getKey();
                Terms.Bucket bucket = entry.getValue();
                Aggregations aggregatedValues = bucket.getAggregations();
                Map<String, Aggregation> aggregationsMap;
                int aggregatedEngagement = 0;
                int aggregatedReach = 0;
                int aggregatedImpression = 0;
                int aggregatedLikeCount = 0;
                int aggregatedCommentCount = 0;
                int aggregatedShareCount = 0;
                int aggregatedClickCount = 0;
                double aggregatedEngagementRate = 0d;
                if(aggregatedValues != null) {
                    aggregationsMap = aggregatedValues.getAsMap();
                    ParsedSum parsedSumEngagement = (ParsedSum) aggregationsMap.get("engagement");
                    ParsedSum parsedSumReach = (ParsedSum) aggregationsMap.get("reach");
                    ParsedSum parsedSumImpression = (ParsedSum) aggregationsMap.get("impression");
                    ParsedSum parsedSumLikeCount = (ParsedSum) aggregationsMap.get("likeCount");
                    ParsedSum parsedSumCommentCount = (ParsedSum) aggregationsMap.get("commentCount");
                    ParsedSum parsedSumShareCount = (ParsedSum) aggregationsMap.get("shareCount");
                    ParsedSum parsedSumClickCount = (ParsedSum) aggregationsMap.get("clickCount");
                    if(parsedSumEngagement !=null) {
                        aggregatedEngagement = (int)parsedSumEngagement.getValue();
                    }
                    if(parsedSumReach !=null) {
                        aggregatedReach = (int)parsedSumReach.getValue();
                    }
                    if(parsedSumImpression !=null) {
                        aggregatedImpression = (int)parsedSumImpression.getValue();
                    }
                    if(parsedSumLikeCount !=null) {
                        aggregatedLikeCount = (int)parsedSumLikeCount.getValue();
                    }
                    if(parsedSumCommentCount !=null) {
                        aggregatedCommentCount = (int)parsedSumCommentCount.getValue();
                    }
                    if(parsedSumShareCount !=null) {
                        aggregatedShareCount = (int)parsedSumShareCount.getValue();
                    }
                    if(parsedSumClickCount !=null) {
                        aggregatedClickCount = (int)parsedSumClickCount.getValue();
                    }
                    aggregatedEngagementRate = calculateEngagementRate(aggregatedEngagement, aggregatedImpression);
                }

                CalendarViewPagePostInsightsData calendarViewPagePostInsightsData = CalendarViewPagePostInsightsData.builder()
                        .socialSite(SocialChannel.getSocialChannelNameById(sourceId))
                        .reach(aggregatedReach)
                        .likeCount(aggregatedLikeCount)
                        .shareCount(aggregatedShareCount)
                        .commentCount(aggregatedCommentCount)
                        .engagement(aggregatedEngagement)
                        .impression(aggregatedImpression)
                        .clickCount(aggregatedClickCount)
                        .engagementRate(aggregatedEngagementRate)
                        .build();
                
                responseMap.put(SocialChannel.getSocialChannelNameById(sourceId), calendarViewPagePostInsightsData);
            }
            return responseMap;
        } catch (Exception e) {
            log.error("Exception for getting the canlendar page post insights data from elastic search for request: {} and error: {}", request, e.getMessage());
        }
        return null;
    }
    @Override
    public Map<String, List<CalendarViewPagePostInsightsData>> getCalendarViewPostDataFromEs(InsightsEsRequestForCalendarPage request) {
        try {
            ESInsightResponse esPageInsightResponse = esService.searchInsightTemplateForCalendarViewPage(request);

            if (Objects.isNull(esPageInsightResponse)) {
                throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_FETCH_FB_INSIGHTS);
            }
            Aggregations bePostAggregations = esPageInsightResponse.getResponse().getAggregations();
            ParsedStringTerms bePostParsedLongTerms = bePostAggregations.get("be_post_id_aggregation");
            List<? extends Terms.Bucket> bePostIdBuckets =  bePostParsedLongTerms.getBuckets();

            Map<String, List<CalendarViewPagePostInsightsData>> responseMap = new HashMap<>();
            for(Terms.Bucket bucket: bePostIdBuckets) {
                String  bePostIdKey = bucket.getKeyAsString();
                Aggregations sourceIdAggregation = bucket.getAggregations();
                ParsedLongTerms sourceIdParsedLongTerms = sourceIdAggregation.get("source_id_aggregation");
                List<? extends Terms.Bucket> sourceIdBuckets = sourceIdParsedLongTerms.getBuckets();
                Map<Integer, ? extends Terms.Bucket> sourceIdVsBucketMap = sourceIdBuckets.stream().collect(Collectors.toMap(s->s.getKeyAsNumber().intValue(), s->s));

                List<CalendarViewPagePostInsightsData> internalList = new ArrayList<>();
                for(Map.Entry<Integer, ? extends Terms.Bucket> entry: sourceIdVsBucketMap.entrySet()) {
                    Integer sourceId = entry.getKey();
                    Terms.Bucket sourceBucket = entry.getValue();
                    Aggregations aggregatedValues = sourceBucket.getAggregations();
                    Map<String, Aggregation> aggregationsMap;
                    int aggregatedEngagement = 0;
                    if(aggregatedValues != null) {
                        aggregationsMap = aggregatedValues.getAsMap();
                        ParsedSum parsedSumEngagement = (ParsedSum) aggregationsMap.get("engagement");
                        if(parsedSumEngagement !=null) {
                            aggregatedEngagement = (int)parsedSumEngagement.getValue();
                        }
                    }
                    CalendarViewPagePostInsightsData calendarViewPagePostInsightsData = CalendarViewPagePostInsightsData.builder()
                            .socialSite(SocialChannel.getSocialChannelNameById(sourceId))
                            .engagement(aggregatedEngagement)
                            .build();
                    internalList.add(calendarViewPagePostInsightsData);
                }
                responseMap.put(bePostIdKey, internalList);
            }

            return responseMap;
        } catch (Exception e) {
            log.error("Exception for getting the calendar post insights data from elastic search for request: {} and error: {}", request, e.getMessage());
        }
        return null;
    }
    private BulkRequest prepareBulkRequestForPage(List<ESPageRequest> esPageRequests, String index) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        BulkRequest bulkRequest = new BulkRequest();
        esPageRequests.forEach(esPageRequest -> {
            String s = JSONUtils.toJSON(esPageRequest);
            try {
                bulkRequest.add(new IndexRequest(index).id(dateFormat
                        .parse(esPageRequest.getDay()).getTime()+"_"+esPageRequest.getPage_id())
                        .routing(String.valueOf(esPageRequest.getEnt_id()))
                        .source(s, XContentType.JSON));
            } catch (ParseException e) {
                e.printStackTrace();
            }
        });
        return bulkRequest;
    }

    private BulkRequest prepareBulkRequestForPost(List<EsPostDataPoint> esPostDataPoints, String index) {
        BulkRequest bulkRequest = new BulkRequest();
        esPostDataPoints.forEach(esPageRequest -> {
            String s = JSONUtils.toJSON(esPageRequest);
            bulkRequest.add(new IndexRequest(index).id(esPageRequest.getPost_id())
                    .routing(String.valueOf(esPageRequest.getEnt_id()))
                    .source(s, XContentType.JSON));
        });
        return bulkRequest;
    }

    private BulkRequest prepareBulkRequestForPageInsightsPostCount(List<ESPageRequest> esPageRequests, String index) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        BulkRequest bulkRequest = new BulkRequest();
        esPageRequests.forEach(esPageRequest -> {
            try {
                Map<String, Object> jsonMap = new HashMap<>();
                jsonMap.put("post_total_count", esPageRequest.getPost_total_count());
                jsonMap.put("business_id", esPageRequest.getBusiness_id());
                jsonMap.put("page_id", esPageRequest.getPage_id());
                jsonMap.put("ent_id", esPageRequest.getEnt_id());
                jsonMap.put("day", esPageRequest.getDay());

                bulkRequest.add(new UpdateRequest(index, dateFormat
                        .parse(esPageRequest.getDay()).getTime()+"_"+esPageRequest.getPage_id()).doc(jsonMap).docAsUpsert(true));
            } catch (ParseException e) {
                e.printStackTrace();
            }
        });
        return bulkRequest;
    }

    @Override
    public void bulkPostPageEngagementBreakDown(List<ESPageRequest> esPageRequest, String index) {
        try {
            BulkRequest bulkRequest = bulkPostPageEngagement(esPageRequest,index);
            esCommonService.addBulkDocument(bulkRequest);
        }catch (IOException e){
            log.error("IOException occurred while posting post data and insights to elastic search error :{}", e.getMessage());
        }
    }

    @Override
    public BoolQueryBuilder prepareQueryToGetCommentCount(String facebookPageId, BackfillInsightReq backfillInsightReq) {
        return QueryBuilders.boolQuery()
                .filter(QueryBuilders.termQuery("pageId.keyword", facebookPageId))
                .filter(QueryBuilders.rangeQuery("feedDate")
                        .gt(backfillInsightReq.getStartDate())
                        .lt(backfillInsightReq.getEndDate()))
                .filter(QueryBuilders.termsQuery("type.keyword", Arrays.asList(COMMENT,AD_COMMENT)));
    }

    @Override
    public SearchSourceBuilder prepareQueryForSeachSourceAndAggregate(BoolQueryBuilder queryBuilder) {
        return new SearchSourceBuilder()
                .size(0)
                .query(queryBuilder)
                .aggregation(AggregationBuilders.dateHistogram("comments_per_day")
                        .field("feedDate").dateHistogramInterval(DateHistogramInterval.DAY)
                        .format("yyyy-MM-dd"));
    }

    public static String convertDateFormat(String dateString,String format) {
        SimpleDateFormat inputFormat = new SimpleDateFormat(format);
        SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            Date date = inputFormat.parse(dateString);
            return outputFormat.format(date);
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public Map<String, Integer> getDataFromEs(SearchSourceBuilder sourceBuilder,String indexName) {
        Map<String, Integer> commentsPerDay = new TreeMap<>();
        try {
            SearchRequest searchRequest = new SearchRequest(indexName);
            searchRequest.source(sourceBuilder);
            SearchResponse response = esCommonService.search(searchRequest);
            ParsedDateHistogram dataByPageId = response.getAggregations().get("comments_per_day");
            List<? extends Histogram.Bucket> histogram = dataByPageId.getBuckets();
            if(CollectionUtils.isEmpty(histogram)){
                log.info("Histogram is empty");
                return commentsPerDay;
            }
            for (Bucket bucket : histogram) {
                String date = convertDateFormat(bucket.getKeyAsString(),"yyyy-MM-dd");
                int count = Math.toIntExact(bucket.getDocCount());
                commentsPerDay.put(date, count);
                log.info("Date :{} and count :{}",date,count);
            }
        } catch (Exception e){
            log.info("Exception occurred while posting es comment data :{}",sourceBuilder);
            throw new BirdeyeSocialException("Exception occurred while fetching comment count",e);
        }
        return commentsPerDay;
    }

    @Override
    public Map<String, Integer> getPostLikeCountFromFB(List<Data> dataList) {
        Map<String, Integer> map = new HashMap<>();
        dataList.forEach(data -> {
            String name = data.getName();
            if (name.equals(InsightsConstants.page_actions_post_reactions_total)) {
                data.getValues().forEach(value -> {
                    try {
                        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'hh:mm:ssZ");
                        SimpleDateFormat output = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
                        Date valueDate = dateFormat.parse(value.getEndTime());
                        valueDate = DateTimeUtils.addTimeInMinutes(valueDate, -24 * 60);
                        String date = output.format(valueDate);
                        Integer pagePostLikeCount = 0;
                        if (value.getValue() instanceof Map) {
                            Map<String, Integer> reactions = (Map<String, Integer>) value.getValue();
                            for (Integer reactionCount : reactions.values()) {
                                pagePostLikeCount += reactionCount;
                            }
                        }
                        map.put(date, pagePostLikeCount);
                    } catch (Exception e) {
                        throw new BirdeyeSocialException(e.getMessage());
                    }
                });
            }
        });
        return map;
    }


    private BulkRequest bulkPostPageEngagement(List<ESPageRequest> esPageRequests, String index) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        BulkRequest bulkRequest = new BulkRequest();
        esPageRequests.forEach(esPageRequest -> {
            try {
                Map<String, Object> jsonMap = new HashMap<>();
                jsonMap.put("post_comment_count", esPageRequest.getPost_total_count());
                jsonMap.put("post_like_count", esPageRequest.getBusiness_id());
                jsonMap.put("post_share_count", esPageRequest.getPage_id());
                bulkRequest.add(new UpdateRequest(index, dateFormat
                        .parse(esPageRequest.getDay()).getTime()+"_"+esPageRequest.getPage_id()).doc(jsonMap).docAsUpsert(true));
            } catch (ParseException e) {
                e.printStackTrace();
            }
        });
        return bulkRequest;
    }

    private String getDateStringWithZeroTime(String dateString) {
        SimpleDateFormat usDateFormat = new SimpleDateFormat(usDateFormatString);
        SimpleDateFormat dateFormatter= new SimpleDateFormat(dateFormatterString);
        Date actualDate, dateWithZeroTime;

        try {
            actualDate = dateFormatter.parse(dateString);
            dateWithZeroTime = usDateFormat.parse(usDateFormat.format(actualDate));
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }

        return dateFormatter.format(dateWithZeroTime);
    }

    private List<String> getPageIdsFromBids(List<Integer> businessIds) {
        List<String> pageIds = new ArrayList<>();
        pageIds.addAll(socialFBPageRepository.findDistinctFacebookPageIdByBusinessIdInAndIsValid(businessIds));
        pageIds.addAll(gmbRepo.findDistinctGmbLocationIdByBusinessIdInAndIsValid(businessIds));
        pageIds.addAll(businessInstagramAccountRepository.findDistinctInstagramIdByBusinessIdIn(businessIds));
        pageIds.addAll(businessLinkedinPageRepo.findDistinctPageIdByBusinessIdInAndIsValid(businessIds));
        pageIds.addAll(Lists.transform(socialTwitterAccountRepository.findProfileIdsByBusinessIdIn(businessIds), Functions.toStringFunction()));
        pageIds.addAll(Lists.transform(socialTwitterAccountRepository.findProfileIdsByBusinessIdIn(businessIds), Functions.toStringFunction()));
        return pageIds;
    }

    @Override
    public PageFollowersData getFollowersResponse(InsightsRequest insights) throws Exception {
        log.info("Request for summary followers: {}", insights);
        PageFollowersData pageFollowersData= new PageFollowersData();
        List<Integer> businessIds=insights.getBusinessIds();
        List<String> pageIdsList = getPageIdsFromBids(businessIds);
        if(CollectionUtils.isEmpty(pageIdsList)){
            log.info("No pageId  found for businessIds in request");
            return null;
        }

        String startDate= startDateFollowerResponse(pageIdsList);

        insights.setStartDate(reportDataConverter.getStartDate(insights,insights.getBusinessIds(),startDate));

        CompletableFuture<Map<String, Integer>> facebook = CompletableFuture.supplyAsync(() -> {
            return getFollowersESResponse(insights,ElasticConstants.FACEBOOK_PAGE_INSIGHTS.getName(),pageIdsList);
        });
        CompletableFuture<Map<String, Integer>> twitter = CompletableFuture.supplyAsync(() -> {
            return  getFollowersESResponse( insights,ElasticConstants.TWITTER_PAGE_INSIGHTS.getName(),pageIdsList);
        });

        CompletableFuture<Map<String, Integer>> linkedin = CompletableFuture.supplyAsync(() -> {
            return getFollowersESResponse( insights,ElasticConstants.LINKEDIN_PAGE_INSIGHTS.getName(),pageIdsList);
        });
        CompletableFuture<Map<String, Integer>> instagram = CompletableFuture.supplyAsync(() -> {
            return  getFollowersESResponse( insights,ElasticConstants.INSTAGRAM_PAGE_INSIGHTS.getName(),pageIdsList);
        });

        CompletableFuture<Map<String, Integer>> gmb = CompletableFuture.supplyAsync(() -> {
            return  getFollowersESResponse( insights,ElasticConstants.GMB_REPORT_PAGE_INSIGHTS.getName(),pageIdsList);
        });

        CompletableFuture<Void> integrationCompletionFuture = CompletableFuture.allOf(facebook,twitter,linkedin,instagram,gmb);

        integrationCompletionFuture.get(100, TimeUnit.SECONDS);

        Map<String, Map<String, Integer>> result=getCombinedMapFollwerData(facebook,linkedin,twitter,gmb,instagram);

        int countNewFollowers = facebook.get().values().stream().reduce(0, Integer::sum) +
                twitter.get().values().stream().reduce(0, Integer::sum) +
                instagram.get().values().stream().reduce(0, Integer::sum) +
                linkedin.get().values().stream().reduce(0, Integer::sum) +
                gmb.get().values().stream().reduce(0, Integer::sum);

        List<PageFollowersDataPoint> pageFollowersDataPointList=populateListFollowerResponse(result);

        pageFollowersData.setTotalNewFollowers(countNewFollowers);
        if(CollectionUtils.isNotEmpty(pageFollowersDataPointList)) {
            pageFollowersDataPointList.sort((m1, m2) -> {
                try {
                    Date first = new SimpleDateFormat(dateForString).parse(m1.getLabel());
                    Date second = new SimpleDateFormat(dateForString).parse(m2.getLabel());
                    return first.compareTo(second);
                } catch (ParseException ex) {
                    log.error("Invalid date Exception{}: {}",  ex.getMessage());
                    throw new BirdeyeSocialException(ErrorCodes.INVALID_FROM_DATE);
                }
            });
        }

        pageFollowersData.setDataPoints(pageFollowersDataPointList);
        InsightsESRequest request = dataFormation(insights, null, pageIdsList);
        pageFollowersData.setDateDiff(getTimeDifference(request));
        pageFollowersData.setGroupByType(request.getDataModel().getType());
        GroupByType groupBy = GroupByType.getByName(insights.getGroupByType());

        getCustomFirstIndexData(insights.getEndDate(), groupBy, pageFollowersData);

        return pageFollowersData;
    }

    @Override
    public Map<Date, Integer> datevsClickCountMap (InsightsESRequest request){
        Map<Date, Integer> dateVsClickCount = new HashMap<>();
        try {
            ESInsightResponse esPageInsightResponse = esService.searchInsightTemplate(request);
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            SearchHits searchHit = esPageInsightResponse.getResponse().getHits();
            if(Objects.nonNull(searchHit)) {
                SearchHit[] searchHits = searchHit.getHits();
                for(SearchHit hit : searchHits) {
                    if(hit != null) {
                        Map<String, Object> json = hit.getSourceAsMap();
                        Date  date = dateFormat.parse(json.get("day").toString());
                        Integer clickCount = (Integer) json.get("click_count");
                        if(date != null && clickCount != null) {
                            dateVsClickCount.put(date, clickCount);
                        }
                    }
                }
            }
            return dateVsClickCount;

        } catch (Exception e) {
            log.info("Exception occurred while fetching dateVsClickCount from es : {}",e.getMessage());
            return dateVsClickCount;
        }
    }

    @Override
    public List<PagePostInsightsData> getDayWisePageInsightDataMapFromES(InsightsESRequest request) {
        List<PagePostInsightsData> dayWisePageInsightData = new ArrayList<>();
        try {
            ESInsightResponse esPageInsightResponse = esService.searchInsightTemplate(request);
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            SearchHits searchHit = esPageInsightResponse.getResponse().getHits();
            if(Objects.nonNull(searchHit)) {
                SearchHit[] searchHits = searchHit.getHits();
                for(SearchHit hit : searchHits) {
                    if(hit != null) {
                        Map<String, Object> json = hit.getSourceAsMap();
                        PagePostInsightsData pagePostInsightsData = new PagePostInsightsData();
                        pagePostInsightsData.setDate(dateFormat.parse(json.get("day").toString()));
                        pagePostInsightsData.setTotalPagePostComments((Integer) json.get("total_post_comment_count"));
                        pagePostInsightsData.setTotalPagePostShares((Integer) json.get("total_post_share_count"));
                        pagePostInsightsData.setTotalPageClickCount((Integer) json.get("total_click_count"));
                        pagePostInsightsData.setCommentCount((Integer) json.get("post_comment_count"));
                        pagePostInsightsData.setShareCount((Integer) json.get("post_share_count"));
                        pagePostInsightsData.setLinkClickCount((Integer) json.get("click_count"));
                        pagePostInsightsData.setOtherClickCount((Integer) json.get("other_click_count"));
                        dayWisePageInsightData.add(pagePostInsightsData);
                    }
                }
            }
            return dayWisePageInsightData;

        } catch (Exception e) {
            log.info("Exception occurred while fetching dayWisePageInsightData from es : {}",e.getMessage());
            return dayWisePageInsightData;
        }
    }

    @Override
    public PerformanceSummaryResponse getPagePerformanceDataFromEsChannelWise(InsightsESRequest request) {
        PerformanceSummaryResponse res = new PerformanceSummaryResponse();
        log.info("Request for performance summary : {}", request);
        try {
            if(Objects.isNull(request)) {
                log.info("request is null sending performance as 0");
                return res;
            }

            ESInsightResponse esPageInsightResponse = esService.searchInsightTemplate(request);

            Aggregations aggregations = esPageInsightResponse.getResponse().getAggregations();

            ParsedFilter data = aggregations.get("data");
           if(Objects.nonNull(data)) {
               ParsedSum parsedSumEngagement = data.getAggregations().get("engagement");
               res.setEngagements((int) parsedSumEngagement.getValue());

               ParsedSum parsedSumImpression = data.getAggregations().get("impression");
               res.setImpressions((int) parsedSumImpression.getValue());

               ParsedSum parsedSumClickCount = data.getAggregations().get("clickCount");
               res.setPostLinkClicks((int) parsedSumClickCount.getValue());

               ParsedSum parsedLikeCount = data.getAggregations().get("likeCount");
               res.setLikeCount((int) parsedLikeCount.getValue());

               ParsedSum parsedShareCount = data.getAggregations().get("shareCount");
               res.setShareCount((int) parsedShareCount.getValue());

               ParsedSum parsedCommentCount = data.getAggregations().get("commentCount");
               res.setCommentCount((int) parsedCommentCount.getValue());


               ParsedTopHits netFollowers = data.getAggregations().get("followers");
               netFollowers.getHits().forEach(e -> {
                   EsPageDataPoint esPageDataPoint = JSONUtils.fromJSON(e.getSourceAsString(), EsPageDataPoint.class);
                   res.setTotalFollowerCount(esPageDataPoint.getTotal_follower_count());
               });

               ParsedSum parsedFollowerGainCount = data.getAggregations().get("followerGain");
               res.setFollowerGain((int) parsedFollowerGainCount.getValue());

               ParsedSum parsedFollowerLostCount = data.getAggregations().get("followerLost");
               res.setFollowerLost((int) parsedFollowerLostCount.getValue());

               double engRate =  formatToTwoDecimalPlaces(calculateEngagementRate(res.getEngagements(), res.getImpressions()));
               res.setEngRate(engRate);
           }

            return res;

        } catch (Exception e) {
            log.info("Exception occurred while fetching data from es : {}",e.getMessage(), e);
            return res;
        }

    }

    @Override
    public Integer getPostLinkClickCountTotal(InsightsESRequest request) {
        Integer clickCount = 0;
        try {
            ESInsightResponse esPageInsightResponse = esService.searchInsightTemplate(request);

            Aggregations aggregations = esPageInsightResponse.getResponse().getAggregations();

            ParsedSum parsedSumClickCount = aggregations.get("clickCount");
            if(Objects.nonNull(parsedSumClickCount)) {
                clickCount = (int) parsedSumClickCount.getValue();
            }

            return clickCount;

        } catch (Exception e) {
            log.info("Exception occurred while fetching data from es : {}",e.getMessage());
            return clickCount;
        }

    }

    @Override
    public Integer getPostOtherClickCountTotal(InsightsESRequest request) {
        Integer otherClickCount = 0;
        try {
            ESInsightResponse esPageInsightResponse = esService.searchInsightTemplate(request);

            Aggregations aggregations = esPageInsightResponse.getResponse().getAggregations();

            ParsedSum parsedSumClickCount = aggregations.get("otherClickCount");
            if(Objects.nonNull(parsedSumClickCount)) {
                otherClickCount = (int) parsedSumClickCount.getValue();
            }

            return otherClickCount;

        } catch (Exception e) {
            log.info("Exception occurred while fetching data from es : {}",e.getMessage());
            return otherClickCount;
        }

    }

    /**
     * Gets summation of Facebook story insights from Elasticsearch
     *
     * @param request The Elasticsearch request containing query parameters
     * @return Map containing story metrics with their values
     */
    public Map<String, Integer> getSummationOfFbStoryInsights(InsightsESRequest request) {
        Map<String, Integer> storyInsights = new HashMap<>();

        try {
            ESInsightResponse esPageInsightResponse = esService.searchInsightTemplate(request);

            Aggregations aggregations = esPageInsightResponse.getResponse().getAggregations();

            // Extract metrics from aggregations
            ParsedValueCount parsedSumFbStoryCount = aggregations.get("publishedStoryCount");
            ParsedSum parsedSumFbStoryImpression = aggregations.get("storyImpressions");
            ParsedSum parsedSumFbStoryEngagement = aggregations.get("storyEngagements");
            ParsedSum parsedSumFbStoryReach = aggregations.get("storyReach");
            ParsedSum parsedSumFbStoryLike = aggregations.get("storyLikes");
            ParsedSum parsedSumFbStoryComment = aggregations.get("storyComments");
            ParsedSum parsedSumFbStoryShare = aggregations.get("storyShares");

            // Add metrics to map with null checks
            if(Objects.nonNull(parsedSumFbStoryCount)) {
                storyInsights.put("storyCount", (int) parsedSumFbStoryCount.getValue());
            } else {
                storyInsights.put("storyCount", 0);
            }

            if(Objects.nonNull(parsedSumFbStoryImpression)) {
                storyInsights.put("storyImpressions", (int) parsedSumFbStoryImpression.getValue());
            } else {
                storyInsights.put("storyImpressions", 0);
            }

            if(Objects.nonNull(parsedSumFbStoryEngagement)) {
                storyInsights.put("storyEngagements", (int) parsedSumFbStoryEngagement.getValue());
            } else {
                storyInsights.put("storyEngagements", 0);
            }

            if(Objects.nonNull(parsedSumFbStoryReach)) {
                storyInsights.put("storyReach", (int) parsedSumFbStoryReach.getValue());
            } else {
                storyInsights.put("storyReach", 0);
            }

            if(Objects.nonNull(parsedSumFbStoryLike)) {
                storyInsights.put("storyLikes", (int) parsedSumFbStoryLike.getValue());
            } else {
                storyInsights.put("storyLikes", 0);
            }

            if(Objects.nonNull(parsedSumFbStoryComment)) {
                storyInsights.put("storyComments", (int) parsedSumFbStoryComment.getValue());
            } else {
                storyInsights.put("storyComments", 0);
            }

            if(Objects.nonNull(parsedSumFbStoryShare)) {
                storyInsights.put("storyShares", (int) parsedSumFbStoryShare.getValue());
            } else {
                storyInsights.put("storyShares", 0);
            }

            return storyInsights;

        } catch (Exception e) {
            log.info("[getSummationOfFbStoryInsights] Exception occurred while fetching data from es: {}", e.getMessage());
            // Return empty map with default values in case of error
            Map<String, Integer> defaultMap = new HashMap<>();
            defaultMap.put("storyCount", 0);
            defaultMap.put("storyImpressions", 0);
            defaultMap.put("storyEngagements", 0);
            defaultMap.put("storyReach", 0);
            defaultMap.put("storyLikes", 0);
            defaultMap.put("storyComments", 0);
            defaultMap.put("storyShares", 0);
            return defaultMap;
        }
    }

    @Override
    public Integer prevLinkClickCount(InsightsESRequest request) {
        Integer clickCount = 0;
        try {
            ESInsightResponse esPageInsightResponse = esService.searchInsightTemplate(request);
            SearchHits searchHit = esPageInsightResponse.getResponse().getHits();
            if(Objects.nonNull(searchHit)) {
                SearchHit[] searchHits = searchHit.getHits();
                if(null != searchHits && searchHits.length > 0) {
                    Map<String, Object> json = searchHits[0].getSourceAsMap();
                    clickCount = (Integer) json.get("click_count");
                }
            }
            return clickCount;

        } catch (Exception e) {
            log.info("Exception occurred while fetching data from es : {}",e.getMessage());
            return clickCount;
        }

    }

    @Override
    public Integer prevOtherClickCount(InsightsESRequest request) {
        Integer clickCount = 0;
        try {
            ESInsightResponse esPageInsightResponse = esService.searchInsightTemplate(request);
            SearchHits searchHit = esPageInsightResponse.getResponse().getHits();
            if(Objects.nonNull(searchHit)) {
                SearchHit[] searchHits = searchHit.getHits();
                if(null != searchHits && searchHits.length > 0) {
                    Map<String, Object> json = searchHits[0].getSourceAsMap();
                    clickCount = (Integer) json.get("other_click_count");
                }
            }
            return clickCount;

        } catch (Exception e) {
            log.info("Exception occurred while fetching data from es : {}",e.getMessage());
            return clickCount;
        }

    }

    public Integer prevPageStoryInsights(InsightsESRequest request, String insightName) {
        Integer insightCount = 0;
        try {
            ESInsightResponse esPageInsightResponse = esService.searchInsightTemplate(request);
            SearchHits searchHit = esPageInsightResponse.getResponse().getHits();
            if(Objects.nonNull(searchHit)) {
                SearchHit[] searchHits = searchHit.getHits();
                if(null != searchHits && searchHits.length > 0) {
                    Map<String, Object> json = searchHits[0].getSourceAsMap();
                    insightCount = (Integer) json.get(insightName);
                }
            }
            return insightCount;

        } catch (Exception e) {
            log.info("[prevPageStoryInsights] Exception occurred while fetching data from es : {}",e.getMessage());
            return insightCount;
        }
    }


    private Double formatToTwoDecimalPlaces(Double number) {
        if(number == null) number = 0d;
        return Double.valueOf(new DecimalFormat("#.#").format(number));
    }
    private Map<String, Map<String, Integer>> getCombinedMapFollwerData(CompletableFuture<Map<String, Integer>> facebook, CompletableFuture<Map<String, Integer>> linkedin, CompletableFuture<Map<String, Integer>> twitter, CompletableFuture<Map<String, Integer>> gmb, CompletableFuture<Map<String, Integer>> instagram) throws ExecutionException, InterruptedException {

        Map<String, Map<String, Integer>> result= new HashMap<>();
        if(Objects.nonNull(facebook.get())){
        result.put("facebook",facebook.get());
         }
        if(Objects.nonNull(linkedin.get())){
            result.put("linkedin",linkedin.get());
        }
        if(Objects.nonNull(twitter.get())){
            result.put("twitter",twitter.get());
        }
        if(Objects.nonNull(gmb.get())){
            result.put("gmb",gmb.get());
        }
        if(Objects.nonNull(instagram.get())){
            result.put("instagram",instagram.get());
        }

         return result;
    }

    private String startDateFollowerResponse(List<String> pageIdsList) throws ExecutionException, InterruptedException, TimeoutException {
        CompletableFuture<String> facebookStartDate = CompletableFuture.supplyAsync(() -> {
            try {
                return getStartDate(pageIdsList,ElasticConstants.FACEBOOK_PAGE_INSIGHTS.getName());
            } catch (Exception e) {
                log.error("Unable to Fetch start date for facebook page insights:{}",e.getMessage());
                throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_FETCH_START_DATE);
            }
        });
        CompletableFuture<String> instaStartDate = CompletableFuture.supplyAsync(() -> {
            try {
                return getStartDate(pageIdsList,ElasticConstants.INSTAGRAM_PAGE_INSIGHTS.getName());
            } catch (Exception e) {
                log.error("Unable to Fetch start date for instagram page insights:{}",e.getMessage());
                throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_FETCH_START_DATE);
            }
        });
        CompletableFuture<String> linkedinStartDate = CompletableFuture.supplyAsync(() -> {
            try {
                return getStartDate(pageIdsList,ElasticConstants.LINKEDIN_PAGE_INSIGHTS.getName());
            } catch (Exception e) {
                log.error("Unable to Fetch start date for linkedin page insights:{}",e.getMessage());
                throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_FETCH_START_DATE);
            }
        });
        CompletableFuture<String> twitterStartDate = CompletableFuture.supplyAsync(() -> {
            try {
                return getStartDate(pageIdsList,ElasticConstants.TWITTER_PAGE_INSIGHTS.getName());
            } catch (Exception e) {
                log.error("Unable to Fetch start date for twitter page insights:{}",e.getMessage());
                throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_FETCH_START_DATE);
            }
        });
        CompletableFuture<String> gmbStartDate = CompletableFuture.supplyAsync(() -> {
            try {
                return getStartDate(pageIdsList,ElasticConstants.GMB_REPORT_PAGE_INSIGHTS.getName());
            } catch (Exception e) {
                log.error("Unable to Fetch start date for gmb page insights:{}",e.getMessage());
                throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_FETCH_START_DATE);
            }
        });

        CompletableFuture<Void> integrationCompletionFutureDate = CompletableFuture.allOf(facebookStartDate,twitterStartDate,linkedinStartDate,instaStartDate,gmbStartDate
        );

        integrationCompletionFutureDate.get(100, TimeUnit.SECONDS);
        List<String> datesFollowers=Arrays.asList(facebookStartDate.get(),twitterStartDate.get(),linkedinStartDate.get(),instaStartDate.get(),gmbStartDate.get());
        return getOldestDate(datesFollowers);

    }

    private String getOldestDate(List<String> datesFollowers) {
        DateTimeFormatter f = DateTimeFormatter.ofPattern( "uuuu-MM-dd HH:mm:ss" );
        String latest =
                datesFollowers
                        .stream()
                        .map( s -> LocalDateTime.parse( s , f ) )
                        .min( LocalDateTime:: compareTo )
                        .get()
                        .toString();
        return latest;
    }

    private InsightsESRequest dataFormation(InsightsRequest insights, String indexName, List<String> pageIdsList)  {
        insights.setReportType(SearchTemplate.NEW_FOLLOWERS_COUNT.getName());

        long diff = insights.getEndDate().getTime() - insights.getStartDate().getTime();
        long noOfDaysBetween = TimeUnit.DAYS.convert(diff, TimeUnit.MILLISECONDS);
        reportDataConverter.setGroupByType(insights,noOfDaysBetween);
        InsightsESRequest request = reportDataConverter.createESRequestForPage(insights,indexName,pageIdsList,null);
        return request;
    }

    public void getCustomFirstIndexData(Date endDate, GroupByType type, PageFollowersData pageFollowersData) throws Exception {

        PageFollowersDataPoint dataPoint = new PageFollowersDataPoint();
        Date startDate;
        SimpleDateFormat dateFormatter = new SimpleDateFormat("MM/dd/yyyy");
        if(CollectionUtils.isNotEmpty(pageFollowersData.getDataPoints())) {
            PageFollowersDataPoint pageFollowersDataPoint = pageFollowersData.getDataPoints().get(0);
            startDate = dateFormatter.parse(pageFollowersDataPoint.getLabel());
            InsightsReportUtil.customizeLabelsSummaryOld(pageFollowersData.getDataPoints(), startDate, endDate, type, false);
        }
        dataPoint.setLabel(InsightsConstants.TOTAL);
        dataPoint.setTotalNewFollowers(pageFollowersData.getTotalNewFollowers());
        pageFollowersData.getDataPoints().add(0, dataPoint);
    }


    private Map<String,Integer> getFollowersESResponse(InsightsRequest insights, String indexName, List<String> pageIds) {

        InsightsESRequest request=dataFormation(insights,indexName,pageIds);

        ESInsightResponse esPageInsightResponse = esService.searchInsightTemplate(request);

        Aggregations aggregations = esPageInsightResponse.getResponse().getAggregations();
        if(Objects.isNull(aggregations)){
            log.info("Aggregations are empty for business ids :{}",request.getDataModel().getBusinessIds());
            return null;
        }

        ParsedFilter data = aggregations.get(DATA);
        log.info("parsed data : {}",data);
        ParsedDateHistogram dateHistogramAggregation = data.getAggregations().get(HISTOGRAM);
        log.info("dateHistogramAggregation data for search template : {}",request.getSearchTemplate());
        if (Objects.isNull(dateHistogramAggregation.getBuckets()) && dateHistogramAggregation.getBuckets().size() == 0){
            log.info("DateHistogramAggregation size is empty for search template :{}",request.getSearchTemplate());
            return null;
        }
        List<? extends Histogram.Bucket> buckets = dateHistogramAggregation.getBuckets();

        Map<String, Integer> map=new TreeMap<>();
        buckets.forEach(bucket -> {
            try {

                Aggregations aggregatedValues = bucket.getAggregations();
                Map<String, Aggregation> aggregationsMap;
                if(aggregatedValues != null) {
                    log.info("Bucket as string : {}",bucket.getKeyAsString());
                    aggregationsMap = aggregatedValues.getAsMap();
                    ParsedSum parsedSumEngagement = (ParsedSum) aggregationsMap.get("follower_gain");

                    if (parsedSumEngagement != null) {
                        map.put(bucket.getKeyAsString(), (int) parsedSumEngagement.getValue());
                    }
                }
                else
                    map.put(bucket.getKeyAsString(),0);
            } catch (Exception e) {
                log.error("Exception for getting the followers count from elastic search for request: {} and error: {}", request, e.getMessage());

            }

        });
        return map;

    }

    private List<PageFollowersDataPoint> populateListFollowerResponse( Map<String, Map<String, Integer>> result){

        List<PageFollowersDataPoint> pageFollowersDataPointList = new ArrayList<>();

        for(Map.Entry<String , Map<String, Integer>> m: result.entrySet()){

            String channel=m.getKey();
            Map<String, Integer> dataMap= m.getValue();
            switch (channel) {
                case "facebook":
                    pageFollowersDataPointList.stream().forEach(data -> {

                            if (dataMap.containsKey(data.getLabel())) {
                                data.setFacebook(dataMap.get(data.getLabel()));
                                dataMap.remove(data.getLabel());
                            } else {
                                data.setFacebook(0);
                            }
                    });
                    dataMap.forEach((date, count) -> {
                        PageFollowersDataPoint dataPoint = new PageFollowersDataPoint();
                        dataPoint.setLabel(date);
                        dataPoint.setShortLabel(date);
                        dataPoint.setFacebook(count == null ? 0 : count);

                        pageFollowersDataPointList.add(dataPoint);
                    });
                    break;
                case "linkedin":
                    pageFollowersDataPointList.stream().forEach(data1 -> {
                            if (dataMap.containsKey(data1.getLabel())) {
                                data1.setLinkedin(dataMap.get(data1.getLabel()));
                                dataMap.remove(data1.getLabel());
                            } else {
                                data1.setLinkedin(0);
                            }
                    });
                    dataMap.forEach((date, count) -> {
                        PageFollowersDataPoint dataPoint = new PageFollowersDataPoint();
                        dataPoint.setLabel(date);
                        dataPoint.setShortLabel(date);
                        dataPoint.setLinkedin(count == null ? 0 : count);

                        pageFollowersDataPointList.add(dataPoint);
                    });
                    break;
                case "instagram":
                    pageFollowersDataPointList.stream().forEach(data1 -> {
                            if (dataMap.containsKey(data1.getLabel())) {
                                data1.setInstagram(dataMap.get(data1.getLabel()));
                                dataMap.remove(data1.getLabel());
                            } else {
                                data1.setInstagram(0);
                            }

                    });
                    dataMap.forEach((date, count) -> {
                        PageFollowersDataPoint dataPoint = new PageFollowersDataPoint();
                        dataPoint.setLabel(date);
                        dataPoint.setShortLabel(date);
                        dataPoint.setInstagram(count == null ? 0 : count);

                        pageFollowersDataPointList.add(dataPoint);
                    });
                    break;
                case "twitter":
                    pageFollowersDataPointList.stream().forEach(data1 -> {
                            if (dataMap.containsKey(data1.getLabel())) {
                                data1.setTwitter(dataMap.get(data1.getLabel()));
                                dataMap.remove(data1.getLabel());
                            } else {
                                data1.setTwitter(0);
                            }
                    });
                    dataMap.forEach((date, count) -> {
                        PageFollowersDataPoint dataPoint = new PageFollowersDataPoint();
                        dataPoint.setLabel(date);
                        dataPoint.setShortLabel(date);
                        dataPoint.setTwitter(count == null ? 0 : count);

                        pageFollowersDataPointList.add(dataPoint);
                    });
                    break;
                case "gmb":
                    pageFollowersDataPointList.stream().forEach(data1 -> {
                            if (dataMap.containsKey(data1.getLabel())) {
                                data1.setGmb(dataMap.get(data1.getLabel()));
                                dataMap.remove(data1.getLabel());
                            } else {
                                data1.setGmb(0);
                            }
                    });

                    dataMap.forEach((date, count) -> {
                        PageFollowersDataPoint dataPoint = new PageFollowersDataPoint();
                        dataPoint.setLabel(date);
                        dataPoint.setShortLabel(date);
                        dataPoint.setGmb(count == null ? 0 : count);

                        pageFollowersDataPointList.add(dataPoint);
                    });
                    break;
            }

        }
        return pageFollowersDataPointList;
    }

    @Override
    public ExecutivePostDataResponse getPostData(InsightsRequest insightsRequest, List<String> pageIdList) {

        String index = ElasticConstants.POST_INSIGHTS.getName();
        InsightsESRequest request = reportDataConverter.createESRequestForExecutiveSumData(insightsRequest,index,pageIdList);
        return getPostDataFromEsChannelWise(request);

    }

    @Override
    public SearchResponse getDataFromESPageIndex(LocationReportRequest insightsRequest,
                                                 ReportSortingCriteria sortingCriteria, SortOrder order,
                                                 Integer startIndex, Integer pageSize, List<String> pageIds,String index) {
        try {
            BoolQueryBuilder query = QueryBuilders.boolQuery().must(QueryBuilders.termsQuery(PAGE_ID, pageIds));
            // Filter Aggregation for Date Range
            FilterAggregationBuilder filterAggregation = AggregationBuilders.filter("current_data",
                    QueryBuilders.boolQuery().must(QueryBuilders.rangeQuery(DAY)
                                    .gte(insightsRequest.getStart())
                                    .lte(insightsRequest.getEnd())));

            // Sub-aggregations for Metrics
            filterAggregation.subAggregation(AggregationBuilders.sum(POST_ENGAGEMENT).field(POST_ENGAGEMENT));
            filterAggregation.subAggregation(AggregationBuilders.sum(POST_IMPRESSIONS).field(POST_IMPRESSIONS));
            filterAggregation.subAggregation(AggregationBuilders.sum(FOLLOWER_GAIN).field(FOLLOWER_GAIN));
            filterAggregation.subAggregation(AggregationBuilders.avg(POST_ENG_RATE).field(POST_ENG_RATE));

            // Terms Aggregation with Sorting
            TermsAggregationBuilder termsAggregation = AggregationBuilders.terms("pages")
                    .field(PAGE_ID)
                    .size(pageIds.size())
                    .subAggregation(filterAggregation);

            // Bucket Sort Aggregation for Pagination
            BucketSortPipelineAggregationBuilder bucketSort = new BucketSortPipelineAggregationBuilder("pagination",
                    Collections.singletonList(new FieldSortBuilder("current_data>"+sortingCriteria.getName()).order(order)))
                    .from(startIndex)
                    .size(pageSize);
            termsAggregation.subAggregation(bucketSort);
            // Build Search Request
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder()
                    .query(query)
                    .size(0)
                    .aggregation(termsAggregation);

            SearchRequest searchRequest = new SearchRequest(index);
            searchRequest.source(searchSourceBuilder);
            // Execute Query
            return esCommonService.search(searchRequest);
        }catch (Exception e){
            log.info("Exception occurred :",e);
            throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_FETCH_FB_INSIGHTS,"IO Exception occurred");
        }
    }

    @Override
    public SearchResponse getDataFromESPostIndex(LocationReportRequest insightsRequest,
                                                 ReportSortingCriteria postSortingCriteria, SortOrder order,
                                                 Integer startIndex, Integer pageSize, List<String> pageIds, Integer sourceId) {
        try {
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.size(0);
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
                    .must(QueryBuilders.termQuery("source_id",sourceId))
                    .must(QueryBuilders.termsQuery("page_id", pageIds));
            searchSourceBuilder.query(boolQuery);
            TermsAggregationBuilder pagesAggregation =
                    AggregationBuilders.terms("pages").field("page_id").size(100);
            FilterAggregationBuilder currentDataAggregation =
                    AggregationBuilders.filter("current_data", QueryBuilders.boolQuery()
                                    .must(QueryBuilders.rangeQuery("posted_date").gte(insightsRequest.getStart())
                                    .lte(insightsRequest.getEnd())))
                    .subAggregation(AggregationBuilders.count("post_count").field("page_id"));
//            BucketSortPipelineAggregationBuilder bucketSort = new BucketSortPipelineAggregationBuilder("pagination",
//                    Collections.singletonList(new FieldSortBuilder("current_data>"+postSortingCriteria.getName()).order(order)))
//                    .from(startIndex)
//                    .size(pageSize);
            pagesAggregation.subAggregation(currentDataAggregation);
//            pagesAggregation.subAggregation(bucketSort);
            searchSourceBuilder.aggregation(pagesAggregation);
            SearchRequest searchRequest = new SearchRequest(ElasticConstants.POST_INSIGHTS.getName());
            searchRequest.source(searchSourceBuilder);
            return esCommonService.search(searchRequest);
        }catch (IOException e) {
            throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_FETCH_FB_INSIGHTS, "IO Exception occurred");
        }
    }

    @Override
    public SearchResponse getDataFromESFollowers(LocationReportRequest insightsRequest,
                                                 ReportSortingCriteria postSortingCriteria, SortOrder order,
                                                 Integer startIndex, Integer pageSize, ArrayList<String> pageIds, String index) {
        try {
            SearchRequest searchRequest = new SearchRequest(index);
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.query(QueryBuilders.boolQuery().must(QueryBuilders.termsQuery(PAGE_ID, pageIds))
                    .must(QueryBuilders.rangeQuery(TOTAL_FOLLOWERS_COUNT).gt(0))
                    .must(QueryBuilders.rangeQuery(DAY).gte(insightsRequest.getStart()).lte(insightsRequest.getEnd())));
            TermsAggregationBuilder termsAgg = AggregationBuilders.terms(TOTAL_FOLLOWERS_COUNT).field(PAGE_ID);
            TopHitsAggregationBuilder topHitsAgg = AggregationBuilders.topHits("latest_time_stamp")
                    .sort(DAY, SortOrder.DESC).size(1);
            termsAgg.subAggregation(topHitsAgg);
            searchSourceBuilder.aggregation(termsAgg);
            searchRequest.source(searchSourceBuilder);
            return esCommonService.search(searchRequest);
        }catch (IOException e) {
            throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_FETCH_FB_INSIGHTS, "IO Exception occurred");
        }
    }

    public ExecutivePostDataResponse getPostDataFromEsChannelWise(InsightsESRequest request) {
        ExecutivePostDataResponse res = new ExecutivePostDataResponse();
        log.info("Request for executive summary : {}", request);
        try {
            if(Objects.isNull(request)) {
                log.info("request is null sending performance as 0");
                return res;
            }

            ESInsightResponse esPageInsightResponse = esService.searchInsightTemplate(request);

            Aggregations aggregations = esPageInsightResponse.getResponse().getAggregations();

            ParsedFilter data = aggregations.get("data");
            if(Objects.nonNull(data)) {
                ParsedValueCount parsedTotalPostCount = data.getAggregations().get("post_count");
                res.setPostCount((int) parsedTotalPostCount.getValue());

                ParsedFilter parsedAIPostCount = data.getAggregations().get("aiPostCount");
                res.setAIPostCount((int) parsedAIPostCount.getDocCount());
            }

            return res;

        } catch (Exception e) {
            log.info("Exception occurred while fetching data from es : {}",e.getMessage());
            return res;
        }

    }

    @Override
    public long getTimeDifference(TrendsReportRequest request) {
        SimpleDateFormat dateFormatter = new SimpleDateFormat(dateFormatterString);

        long diff = 0;
        try {
            diff = dateFormatter.parse(dateFormatter.format(request.getEndDate())).getTime() -
                    dateFormatter.parse(dateFormatter.format(request.getStartDate())).getTime();

            long oneDayInMillis = 86400000l;
            long re = diff % oneDayInMillis;
            if(oneDayInMillis - re == 1000l) {
                diff+=1000l;
            }
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        return TimeUnit.DAYS.convert(diff, TimeUnit.MILLISECONDS);
    }

    @Override
    public List<ESPageRequest> getPageWiseLatestData(String index, List<String> pageIds, Date from, Date endDate) {
        List<ESPageRequest> response = new ArrayList<>();
        SearchRequest searchRequest = new SearchRequest(index);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder b = new BoolQueryBuilder();
        b.must(QueryBuilders.termsQuery("page_id", pageIds));
        SimpleDateFormat dateFormatter= new SimpleDateFormat(dateFormatterString);
        RangeQueryBuilder rangeQueryBuilder = new RangeQueryBuilder("day");
        rangeQueryBuilder.gte(dateFormatter.format(from));
        rangeQueryBuilder.lte(dateFormatter.format(endDate));
        b.must(rangeQueryBuilder);
        searchSourceBuilder.query(b);
        searchSourceBuilder.aggregation(AggregationBuilders
                .terms("pageId_agg")
                .field("page_id")
                .size(10000)
                .subAggregation(AggregationBuilders
                        .topHits("latest_hits")
                        .sort("day", SortOrder.DESC)
                        .size(1)));

        searchRequest.source(searchSourceBuilder);
        try {
                SearchResponse searchResponse = esCommonService.search(searchRequest);
        ParsedTerms pageIdAgg = searchResponse.getAggregations().get("pageId_agg");
        for (Terms.Bucket bucket : pageIdAgg.getBuckets()) {
            TopHits topHits = bucket.getAggregations().get("latest_hits");
            topHits.getHits().forEach(hit -> {
                ESPageRequest esPageDataPoint = JSONUtils.fromJSON(hit.getSourceAsString(), ESPageRequest.class);
                response.add(esPageDataPoint);
            });
        }
        }catch (Exception e){
            throw new BirdeyeSocialException("Exception occurred while getting Page Wise LatestData",e);
        }
        return response;
    }

    @Override
    public Integer getPublishedPostData(String index, List<String> pageIds, Date from, Date endDate) {
        SearchRequest searchRequest = new SearchRequest(index);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder b = new BoolQueryBuilder();
        b.must(QueryBuilders.termsQuery("page_id", pageIds));
        SimpleDateFormat dateFormatter= new SimpleDateFormat(dateFormatterString);
        RangeQueryBuilder rangeQueryBuilder = new RangeQueryBuilder("posted_date");
        rangeQueryBuilder.gte(dateFormatter.format(from));
        rangeQueryBuilder.lte(dateFormatter.format(endDate));
        b.must(rangeQueryBuilder);
        searchSourceBuilder.query(b);
        searchRequest.source(searchSourceBuilder);
        try {
            SearchResponse searchResponse = esCommonService.search(searchRequest);
            return searchResponse.getHits().getHits().length;
        }catch (Exception e){
            log.error("Exception occurred while fetching total published posts in performance summary", e);
        }
        return 0;
    }

    @Override
    public List<ProfilePerformanceExcelResponse> getTiktokDemographicReportData(InsightsRequest insightsRequest,
                                                                                String channel) {
        String index = ElasticConstants.TIKTOK_PAGE_INSIGHTS.getName();
        List<String> pageIds = businessTiktokAccountsRepository.findProfileIdByBusinessIdIn(insightsRequest.getBusinessIds());
        if(CollectionUtils.isEmpty(pageIds)){
            return null;
        }
        List<ESPageRequest> esResponse = getPageWiseLatestData(index, pageIds, insightsRequest.getStartDate(), insightsRequest.getEndDate());
        List<ProfilePerformanceExcelResponse> responses = new ArrayList<>();
        SearchTemplate searchTemplate = SearchTemplate.searchTemplate(insightsRequest.getReportType());
        try {
            switch (searchTemplate) {
                case GENDER_BASED_INSIGHTS:
                    getGenderDataForReport(esResponse, responses, channel);
                    break;
                case COUNTRY_BASED_INSIGHTS:
                    getCountryDataForReport(esResponse, responses, channel);
                    break;
                case CITY_BASED_INSIGHTS:
                    getCityDataForReport(esResponse, responses, channel);
                    break;
                default:
                    log.info("Profile report : Invalid search template {}", searchTemplate);
                    break;
            }
            return responses;
        } catch (Exception ex) {
            log.info("Something went wrong while parsing report data for profile performance excel report: {}", esResponse,  ex);
            return new ArrayList<>();
        }
    }

    @Override
    public List<ProfilePerformanceExcelResponse> getChannelSpecificReportData(Map<String, PageReportEsData> map, InsightsRequest insightsRequest) {
        List<ProfilePerformanceExcelResponse> responses = new ArrayList<>();
        SearchTemplate searchTemplate = SearchTemplate.searchTemplate(insightsRequest.getReportType());
        try {
            for (Map.Entry<String, PageReportEsData> entry : map.entrySet()) {
                List<? extends Terms.Bucket> net = entry.getValue().getBuckets();
                SimpleDateFormat dateFor = new SimpleDateFormat(dateForString);
                String channel = entry.getKey();

                if (Objects.requireNonNull(searchTemplate) == SearchTemplate.REPORT_AUDIENCE_GROWTH) {
                    getAudienceGrowthForChannelSpecificReport(net, responses, dateFor, channel);
                } else {
                    log.info("Profile report : Invalid search template {}", searchTemplate);
                }
            }
            return responses;
        } catch (Exception ex) {
            log.info("Something went wrong while parsing report data for profile performance excel report: {}", map,  ex);
            return new ArrayList<>();
        }
    }

    private void getCountryDataForReport(List<ESPageRequest> esPageRequests,
                                        List<ProfilePerformanceExcelResponse> responses,  String channel) {
        if(CollectionUtils.isEmpty(esPageRequests)) return;
        esPageRequests.forEach(bucket -> {
            try {
                String pageId = bucket.getPage_id();
                ProfilePerformanceExcelResponse esResponse ;
                for(DemographicsInsightDataPoint.Data country : bucket.getAudience_countries()) {
                    esResponse = new ProfilePerformanceExcelResponse();
                    esResponse.setChannel(channel);
                    esResponse.setProfileId(pageId);
                    esResponse.setCountryName(country.getLabel());
                    esResponse.setTotalCount(country.getTotal());
                    responses.add(esResponse);
                }
            } catch (Exception e) {
                log.info("Exception occurred : {}", e.getMessage());
            }
        });
    }

    private void getCityDataForReport(List<ESPageRequest> esPageRequests,
                                         List<ProfilePerformanceExcelResponse> responses ,String channel) {
        if(CollectionUtils.isEmpty(esPageRequests)) return;
        esPageRequests.forEach(bucket -> {
            try {
                String pageId = bucket.getPage_id();
                ProfilePerformanceExcelResponse esResponse;
                for(DemographicsInsightDataPoint.Data city : bucket.getAudience_cities()) {
                    esResponse = new ProfilePerformanceExcelResponse();
                    esResponse.setChannel(channel);
                    esResponse.setProfileId(pageId);
                    esResponse.setCityName(city.getLabel());
                    esResponse.setTotalCount(city.getTotal());
                    responses.add(esResponse);
                }
            }catch (Exception e) {
                log.info("Exception occurred : {}", e.getMessage());
            }
        });
    }

    private void getGenderDataForReport(List<ESPageRequest> esPageRequests,
                                        List<ProfilePerformanceExcelResponse> responses, String channel) {
        if(CollectionUtils.isEmpty(esPageRequests)
        ) return;
        esPageRequests.forEach(bucket -> {
            try {
                String pageId = bucket.getPage_id();
                ProfilePerformanceExcelResponse esResponse = new ProfilePerformanceExcelResponse();
                esResponse.setChannel(channel);
                esResponse.setProfileId(pageId);
                for(DemographicsInsightDataPoint.Data audience : bucket.getAudience_genders()) {
                    switch (audience.getName().toLowerCase()) {
                        case "male" :
                            esResponse.setMales(audience.getTotal());
                            break;
                        case "female" :
                            esResponse.setFemales(audience.getTotal());
                            break;
                        default:
                            esResponse.setOthers(audience.getTotal());
                            break;
                    }
                }
                responses.add(esResponse);
            }catch (Exception e) {
                log.info("Exception occurred : {}", e.getMessage());
            }
        });
    }

    @Override
    public ReferencePostViewPostInsightData getReferencePagePostDataFromEs(String postId, ReferencePostDetails referencePostDetails) {
        SearchRequest searchRequest = new SearchRequest(ElasticConstants.POST_INSIGHTS.getName());
        ReferencePostViewPostInsightData result = new ReferencePostViewPostInsightData();
        try {
            BoolQueryBuilder boolQuery;
            if (referencePostDetails.getMasterPostId() != null) { // if masterPostId exists (internal post)
                Integer masterPostId =  referencePostDetails.getMasterPostId(); //need to get all socialPostIds for the masterPostId for creating aggregations across channels
                List<Integer> socialPostIds = socialPostRepository.findIdByMasterPostId(masterPostId);
                if (CollectionUtils.isNotEmpty(socialPostIds)) {
                    List<String> socialPostIdsString = socialPostIds.stream().map(String::valueOf).collect(Collectors.toList());
                    boolQuery = QueryBuilders.boolQuery()
                            .filter(QueryBuilders.termsQuery("be_post_id", socialPostIdsString));
                } else {
                    boolQuery = QueryBuilders.boolQuery()
                            .filter(QueryBuilders.termQuery("post_id", postId));
                }
            } else { // if masterPostId does not exist(external post)
                boolQuery = QueryBuilders.boolQuery()
                    .filter(QueryBuilders.termQuery("post_id", postId));
            }

            SumAggregationBuilder likeCount = AggregationBuilders.sum("likeCount").field("like_count");
            SumAggregationBuilder commentCount = AggregationBuilders.sum("commentCount").field("comment_count");
            SumAggregationBuilder shareCount = AggregationBuilders.sum("shareCount").field("share_count");
            SumAggregationBuilder engagement = AggregationBuilders.sum("engagement").field("engagement");
            SumAggregationBuilder impression = AggregationBuilders.sum("impression").field("impression");

            TermsAggregationBuilder bySourceIdAgg = AggregationBuilders.terms("source_id_aggregation")
                    .field("source_id")
                    .size(MAX_SELF_PAGE_LIMIT)
                    .subAggregation(shareCount)
                    .subAggregation(engagement)
                    .subAggregation(impression)
                    .subAggregation(likeCount)
                    .subAggregation(commentCount);

            // Building the search source
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.query(boolQuery);
            searchSourceBuilder.fetchSource(new String[] {"post_content","image_urls", "posted_date"}, null);
            searchSourceBuilder.aggregation(bySourceIdAgg);
            searchSourceBuilder.size(1);
            searchRequest.source(searchSourceBuilder);
            // Execute the search
            SearchResponse searchResponse = esExecuteService.search(searchRequest);
            SearchHit[] hits = searchResponse.getHits().getHits();
            if (hits != null && hits.length > 0) {
                // Retrieve post_content and image_urls from the first hit
                Map<String, Object> source = hits[0].getSourceAsMap();
                List<String> imageUrls = (List<String>) source.get("image_urls");
                String postContent = (String) source.get("post_content");
                String publishDate = (String) source.get("posted_date");
//              Set these fields in the result object
                result.setText(postContent);
                result.setImageUrls(imageUrls);
                result.setPublishDate(publishDate);
            }
                Aggregations aggregations = searchResponse.getAggregations();
                if (Objects.isNull(aggregations)) {
                    log.info("empty aggregation");
                    return null;
                }

                ParsedLongTerms parsedLongTerms = aggregations.get("source_id_aggregation");

                List<? extends Terms.Bucket> buckets = parsedLongTerms.getBuckets();
                Map<Integer, ? extends Terms.Bucket> sourceIdVsBucketMap =
                        buckets.stream().collect(Collectors.toMap(s -> s.getKeyAsNumber().intValue(), Function.identity()));

                Map<String, ReferencePostViewPostInsightData.PostViewPostInsightData> responseMap = new HashMap<>();
                for (Map.Entry<Integer, ? extends Terms.Bucket> entry : sourceIdVsBucketMap.entrySet()) {
                    Integer sourceId = entry.getKey();
                    Terms.Bucket bucket = entry.getValue();
                    Aggregations aggregatedValues = bucket.getAggregations();
                    Map<String, Aggregation> aggregationsMap;
                    int aggregatedEngagement = 0;
                    int aggregatedImpression = 0;
                    int aggregatedLikeCount = 0;
                    int aggregatedCommentCount = 0;
                    int aggregatedShareCount = 0;
                    if (aggregatedValues != null) {
                        aggregationsMap = aggregatedValues.getAsMap();
                        ParsedSum parsedSumEngagement = (ParsedSum) aggregationsMap.get("engagement");
                        ParsedSum parsedSumImpression = (ParsedSum) aggregationsMap.get("impression");
                        ParsedSum parsedSumLikeCount = (ParsedSum) aggregationsMap.get("likeCount");
                        ParsedSum parsedSumCommentCount = (ParsedSum) aggregationsMap.get("commentCount");
                        ParsedSum parsedSumShareCount = (ParsedSum) aggregationsMap.get("shareCount");
                        if (parsedSumEngagement != null) {
                            aggregatedEngagement = (int) parsedSumEngagement.getValue();
                        }
                        if (parsedSumImpression != null) {
                            aggregatedImpression = (int) parsedSumImpression.getValue();
                        }
                        if (parsedSumLikeCount != null) {
                            aggregatedLikeCount = (int) parsedSumLikeCount.getValue();
                        }
                        if (parsedSumCommentCount != null) {
                            aggregatedCommentCount = (int) parsedSumCommentCount.getValue();
                        }
                        if (parsedSumShareCount != null) {
                            aggregatedShareCount = (int) parsedSumShareCount.getValue();
                        }
                    }

                    ReferencePostViewPostInsightData.PostViewPostInsightData insightData = ReferencePostViewPostInsightData.PostViewPostInsightData.builder()
                            .socialSite(SocialChannel.getSocialChannelNameById(sourceId))
                            .likeCount(aggregatedLikeCount)
                            .commentCount(aggregatedCommentCount)
                            .shareCount(aggregatedShareCount)
                            .engagement(aggregatedEngagement)
                            .impression(aggregatedImpression)
                            .build();

                    responseMap.put(SocialChannel.getSocialChannelNameById(sourceId), insightData);
                    result.setPostViewPostInsightDataMap(responseMap);
                }
                return result;
            } catch (Exception e) {
                log.error("Exception for getting the calendar page post insights data from elastic search for postId: {} and error: {}", postId, e.getMessage());
            }
            return null;
        }

    public List<ESPageRequest> getPageInsightsForDateRange(String pageId, long since, long until) {
        // Build the Elasticsearch query
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery()
                .must(QueryBuilders.termQuery("page_id", pageId))
                .must(QueryBuilders.rangeQuery("day")
                        .gte(since)
                        .lte(until));

        // Create the search request
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(queryBuilder);

        // Execute the search and parse the results
        return executeSearchAndParseResults(searchSourceBuilder, ElasticConstants.INSTAGRAM_PAGE_INSIGHTS.getName());
    }
    private List<ESPageRequest> executeSearchAndParseResults(SearchSourceBuilder searchSourceBuilder, String indexName) {
        List<ESPageRequest> esPageRequests = new ArrayList<>();
        try {
            // Create and configure the search request
            SearchRequest searchRequest = new SearchRequest(indexName);
            searchRequest.source(searchSourceBuilder);

            // Execute the search
            SearchResponse searchResponse = esCommonService.search(searchRequest);

            // Parse the search hits
            SearchHit[] searchHits = searchResponse.getHits().getHits();
            for (SearchHit hit : searchHits) {
                // Convert each hit into an ESPageRequest object
                ESPageRequest esPageRequest = JSONUtils.fromJSON(hit.getSourceAsString(), ESPageRequest.class);
                esPageRequests.add(esPageRequest);
            }
        } catch (Exception e) {
            log.error("Error executing search and parsing results for index {}: {}", indexName, e.getMessage());
        }
        return esPageRequests;
    }

}