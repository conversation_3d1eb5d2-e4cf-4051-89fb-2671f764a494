package com.birdeye.social.service.SocialReportService.Instagram;

import com.birdeye.social.constant.Constants;
import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.dao.BusinessInstagramAccountRepository;
import com.birdeye.social.dao.reports.InstagramPageEngagementResetRepo;
import com.birdeye.social.dao.reports.InstagramPageInsightRepo;
import com.birdeye.social.dao.reports.SocialReportPropertyRepository;
import com.birdeye.social.dto.report.SocialScanEventDTO;
import com.birdeye.social.entities.BusinessInstagramAccount;
import com.birdeye.social.entities.report.InstagramPageInsight;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.insights.ES.Request.DataModel;
import com.birdeye.social.insights.ES.Request.ESPageRequest;
import com.birdeye.social.insights.ES.Request.InsightsESRequest;
import com.birdeye.social.insights.ES.SearchTemplate;
import com.birdeye.social.insights.Instagram.ExternalApiResponse.IgData;
import com.birdeye.social.insights.Instagram.ExternalApiResponse.InstagramAccountDataResponse;
import com.birdeye.social.insights.Instagram.InstagramInsightRequest;
import com.birdeye.social.insights.PageInsightDataPoint;
import com.birdeye.social.insights.PageInsights;
import com.birdeye.social.insights.PageLevelMetaData;
import com.birdeye.social.insights.constants.ElasticConstants;
import com.birdeye.social.insights.constants.InstagramPageEngagementMetric;
import com.birdeye.social.insights.constants.InstagramPageInsightMetric;
import com.birdeye.social.model.BackfillInsightReq;
import com.birdeye.social.model.BackfillRequest;
import com.birdeye.social.model.IgBackFillInsightReq;
import com.birdeye.social.service.SocialReportService.Converter.DbDataConverter;
import com.birdeye.social.service.SocialReportService.Converter.ReportDataConverter;
import com.birdeye.social.service.SocialReportService.ES.ReportsEsService;
import com.birdeye.social.service.SocialReportService.ExternalService.InstagramExternalApiService;
import com.birdeye.social.utils.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class InstagramReportServiceImpl implements InstagramReportService{

    @Autowired
    private BusinessInstagramAccountRepository businessInstagramAccountRepository;

    @Autowired
    private ReportDataConverter reportDataConverter;

    @Autowired
    private InstagramExternalApiService instagramExternalApiService;

    @Autowired
    private KafkaProducerService kafkaProducerService;

    @Autowired
    private ReportsEsService reportsEsService;

    @Autowired
    private DbDataConverter dbDataConverter;

    @Autowired
    private InstagramPageInsightRepo instagramPageInsightRepo;

    @Autowired
    private InstagramPageEngagementResetRepo instagramPageEngagementResetRepo;
    @Autowired
    private SocialProxyHandler socialProxyHandler;
    @Autowired
    private SocialReportPropertyRepository reportPropertyRepository;
    private static final String DATE_FORMAT_STRING = "yyyy-MM-dd'T'hh:mm:ssZ";

   // private static final SimpleDateFormat DATE_FORMATTER = new SimpleDateFormat("yyyy-MM-dd");

    private final static Logger LOG = LoggerFactory.getLogger(InstagramReportServiceImpl.class);

    @Override
    public void getPageInsightsFromInstagram(SocialScanEventDTO socialScanEventDTO) {
        String pageId = socialScanEventDTO.getExternalId();
        if(pageId == null ) {
            return;
        }
        List<String> pageIds = new ArrayList<>();
        pageIds.add(pageId);
        List<BusinessInstagramAccount> businessInstagramAccounts =
                businessInstagramAccountRepository.findByInstagramAccountIdInAndBusinessIdIsNotNull(pageIds);
        Date endDate = Date.from(LocalDate.now().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
        Integer minBackfillDays= reportPropertyRepository.findMinDaysForDailySyncForSourceIdAndReportType(
                SocialChannel.INSTAGRAM.getId(), "profile_matric",InstagramPageInsightMetric.getListOfMetric());
        Date startDate = Date.from(LocalDate.now().minusDays(minBackfillDays).atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
        businessInstagramAccounts.forEach(igAccount -> {
            InstagramInsightRequest instagramInsightRequest = reportDataConverter.createInstagramRequest(igAccount,
                    endDate, startDate);
            pushDataToKafkaForEsUpdate(igAccount,instagramInsightRequest,minBackfillDays);
        });
    }

    public void updateImpressionsForPage(String pageId, long since, long until) {
        try {
            LocalDate startDate = Instant.ofEpochSecond(since).atZone(ZoneId.systemDefault()).toLocalDate();
            LocalDate endDate = Instant.ofEpochSecond(until).atZone(ZoneId.systemDefault()).toLocalDate();

            // Fetch existing data from Elasticsearch for the given pageId and date range
            List<ESPageRequest> existingData = reportsEsService.getPageInsightsForDateRange(pageId, since, until);
            LOG.info("Fetched existing data from ES for pageId: {}, since: {}, until: {}, count: {}",
                    pageId, since, until, existingData.size());

            BusinessInstagramAccount firstBusinessInstagramAccount = null;
            List<BusinessInstagramAccount> businessInstagramAccounts =
                    businessInstagramAccountRepository.findByInstagramAccountIdInAndBusinessIdIsNotNull(Collections.singletonList(pageId));

            if (!businessInstagramAccounts.isEmpty()) {
                firstBusinessInstagramAccount = businessInstagramAccounts.get(0);
            } else {
                LOG.warn("No BusinessInstagramAccount found for pageId: {}", pageId);
                throw new RuntimeException("No BusinessInstagramAccount found for pageId: " + pageId);
            }

            // Map to store existing data by date
            Map<String, ESPageRequest> existingDataMap = existingData.stream()
                    .collect(Collectors.toMap(ESPageRequest::getDay, Function.identity()));

            while (!startDate.isAfter(endDate)) {
                // Convert the current day's start and end to epoch seconds
                long dayStart = startDate.atStartOfDay(ZoneId.systemDefault()).toEpochSecond();
                long dayEnd = startDate.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toEpochSecond() - 1;

                InstagramInsightRequest request = new InstagramInsightRequest();
                request.setPageId(pageId);
                request.setSince(dayStart);
                request.setUntil(dayEnd);
                request.setMetric("views");
                request.setAccessToken(firstBusinessInstagramAccount.getPageAccessToken());

                // Fetch insights for the current day
                List<IgData> insights = instagramExternalApiService.getInstagramAccountInsights(request, false);


                // Process insights and update Elasticsearch
                List<ESPageRequest> esPageRequests = new ArrayList<>();
                for (IgData data : insights) {
                    if ("views".equals(data.getName())) {
                        // Format the day to match the required format
                        String day = DateTimeUtils.localToESFormat(
                                Date.from(startDate.atStartOfDay(ZoneId.systemDefault()).toInstant()),
                                "yyyy-MM-dd HH:mm:ss"
                        );
                        Integer impressions = (Integer) data.getTotalValue().getValue();

                        // Check if the date already exists in Elasticsearch
                        ESPageRequest esPageRequest = existingDataMap.getOrDefault(day, new ESPageRequest());
                        esPageRequest.setDay(day);
                        esPageRequest.setPage_id(pageId);
                        esPageRequest.setPost_impressions(impressions);
                        LOG.info("Updating ES for pageId: {}, day: {}, impressions: {}, esPageRequest : {}",
                                pageId, day, impressions, esPageRequest);
                        esPageRequests.add(esPageRequest);
                    }
                }

                // Bulk update Elasticsearch
                if (!esPageRequests.isEmpty()) {
                    reportsEsService.bulkPostPageInsights(esPageRequests, ElasticConstants.INSTAGRAM_PAGE_INSIGHTS.getName());
                }

                // Move to the next day
                startDate = startDate.plusDays(1);
            }
        } catch (Exception e){
            LOG.error("Error occurred while updating impressions for . Exception: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to update impressions for pageId: " + pageId, e);
        }
    }

    private void pushDataToKafkaForEsUpdate(BusinessInstagramAccount igAccount,
                                            InstagramInsightRequest instagramInsightRequest,Integer size) {
        List<IgData> instagramResponse = socialProxyHandler.runWithRetryableBirdeyeExceptionWithSingleRetry(() ->
                instagramExternalApiService.getInstagramAccountInsights(instagramInsightRequest,false));
        InstagramAccountDataResponse instagramAccountData = socialProxyHandler.runWithRetryableBirdeyeExceptionWithSingleRetry(() ->
                instagramExternalApiService.getInstagramAccountData(instagramInsightRequest));
        Integer mediaCount = instagramAccountData.getMediaCount();
        Integer followerCount = instagramAccountData.getFollowersCount();
        DataModel engagementDataModel = DataModel.builder()
                .pageIds("\""+igAccount.getInstagramAccountId()+"\"")
                .build();
        InsightsESRequest engagementRequest = new InsightsESRequest(engagementDataModel, SearchTemplate.POST_INSIGHT_PAGE_ID_ENGAGEMENT_SUM);
        engagementRequest.setRouting(igAccount.getEnterpriseId().toString());
        engagementRequest.setIndex(ElasticConstants.POST_INSIGHTS.getName());
        Integer currEngagement = reportsEsService.getSumEngagementOnPageId(engagementRequest);

        engagementRequest.setSearchTemplate(SearchTemplate.POST_INSIGHT_PAGE_ID_VIDEO_VIEWS_SUM);
        Integer currVideoViews = reportsEsService.getSumVideoViewsOnPageId(engagementRequest);
        Map<Date, List<IgData>> engagementDataByDay = getEngagementDataMap(instagramInsightRequest,false);


        DataModel dataModel = DataModel.builder()
                .pageIds(igAccount.getInstagramAccountId())
                .build();
        InsightsESRequest insightsESRequest = new InsightsESRequest(dataModel, SearchTemplate.PAGE_INSIGHT_FOR_PREV_DATE);
        insightsESRequest.setIndex(ElasticConstants.INSTAGRAM_PAGE_INSIGHTS.getName());
        insightsESRequest.setRouting(igAccount.getEnterpriseId().toString());

        List<PageInsightDataPoint> pageInsightDataPoints = reportsEsService.getPageInsightHitsDataFromEs(insightsESRequest);

        List<PageLevelMetaData> pageLevelMetaDataList = reportDataConverter.prepareDayWiseDataForInstagram(instagramResponse, igAccount.getInstagramAccountId(),
                igAccount.getBusinessId());
        String ids = pageLevelMetaDataList.stream()
                .map(pageLevelMetaData -> getIdFromPageLevel(pageLevelMetaData.getDate(), igAccount.getInstagramAccountId(),true))
                .collect(Collectors.joining(", "));
        insightsESRequest.setSearchTemplate(SearchTemplate.PAGE_INSIGHT_BY_IDS);
        dataModel.setPageIds(ids);
        dataModel.setSize(size);
        List<PageInsightDataPoint> pageInsightDataPointsByIds = reportsEsService.getPageInsightHitsDataFromEs(insightsESRequest);
        LOG.info(pageInsightDataPointsByIds.toString());
        calculatePostGainAndEngagementGain(pageLevelMetaDataList, pageInsightDataPoints, mediaCount, currEngagement,
                followerCount, currVideoViews,pageInsightDataPointsByIds, igAccount.getInstagramAccountId(),engagementDataByDay);

        PageInsights pageInsights = new PageInsights(igAccount.getEnterpriseId(),igAccount.getInstagramAccountId(),
                igAccount.getBusinessId(), SocialChannel.INSTAGRAM.getName(), pageLevelMetaDataList);

        kafkaProducerService.sendObjectV1(Constants.INSTAGRAM_PAGE_INSIGHTS, pageInsights);
        upsertPageInsights(pageLevelMetaDataList, igAccount.getInstagramAccountId(), igAccount.getBusinessId(),igAccount.getEnterpriseId());
        //addInsightsToDB(pageInsights);
    }

    public Map<Date, List<InstagramPageInsight>> transformRecords(List<InstagramPageInsight> existingRecords) {
        return existingRecords.stream()
                .map(record -> new AbstractMap.SimpleEntry<>(extractMaxDate(record), record))
                .filter(entry -> entry.getKey() != null)
                .collect(Collectors.groupingBy(
                        Map.Entry::getKey,
                        Collectors.mapping(Map.Entry::getValue, Collectors.toList())
                ));

    }

    private Date extractMaxDate(InstagramPageInsight record) {
        try {
            PageInsights pageInsights = JSONUtils.fromJSON(record.getData(), PageInsights.class);

            return pageInsights.getPageInsights().stream()
                    .map(PageLevelMetaData::getDate)
                    .max(Date::compareTo)
                    .orElse(null);
        } catch (Exception e) {
            LOG.info("Exception occurred while extracting instagram max date for id :{} and exception:{}",record.getId(),e.getMessage());
            return null;
        }
    }

    public void upsertPageInsights(List<PageLevelMetaData> pageLevelMetaDataList, String pageId, Integer businessId, Long enterpriseId) {

        if (pageLevelMetaDataList == null || pageLevelMetaDataList.isEmpty()) {
            return;
        }
        Date minDate = pageLevelMetaDataList.stream()
                .map(PageLevelMetaData::getDate)
                .min(Date::compareTo)
                .orElseThrow(() -> new IllegalArgumentException("No valid dates found"));

        Date startOfMinDate = TimeZoneUtil.normalizeToStartOfDay(minDate);

        Date endOfMaxDate = TimeZoneUtil.normalizeToEndOfDay(new Date());

        List<InstagramPageInsight> existingRecords = instagramPageInsightRepo.findByPageIdAndDateBetween(pageId, startOfMinDate, endOfMaxDate);

        Map<Date, List<InstagramPageInsight>> existingRecordMap = transformRecords(existingRecords);

        List<InstagramPageInsight> toUpdate = new ArrayList<>();
        List<InstagramPageInsight> toInsert = new ArrayList<>();

        Date now = new Date();
        Date nextSyncDate = DateUtils.addDays(now, 1);

        for (PageLevelMetaData metaData : pageLevelMetaDataList) {
            PageInsights pageInsights = new PageInsights(
                    enterpriseId,
                    pageId,
                    businessId,
                    SocialChannel.INSTAGRAM.getName(),
                    Arrays.asList(metaData)
            );
            List<InstagramPageInsight> existingPageInsight = existingRecordMap.get(metaData.getDate());

            if (CollectionUtils.isNotEmpty(existingPageInsight)) {
                List<InstagramPageInsight> updatedInsights = existingPageInsight.stream()
                        .map(pageInsight -> {
                            pageInsight.setBusinessId(businessId);
                            pageInsight.setEnterpriseId(enterpriseId);
                            pageInsight.setLastSyncDate(now);
                            pageInsight.setNextSyncDate(nextSyncDate);
                            pageInsight.setData(JSONUtils.toJSON(pageInsights));
                            return pageInsight;
                        })
                        .collect(Collectors.toList());
                toUpdate.addAll(updatedInsights);
            } else {
                InstagramPageInsight newPageInsight = dbDataConverter.convertPageInsightForInstagram(pageInsights);
                toInsert.add(newPageInsight);
            }
        }
        if (!toUpdate.isEmpty()) {
            instagramPageInsightRepo.save(toUpdate);
        }
        if (!toInsert.isEmpty()) {
            instagramPageInsightRepo.save(toInsert);
        }
    }

    private Map<Date,List<IgData>> getEngagementDataMap(InstagramInsightRequest instagramInsightRequest,boolean isBackFill) {
        Map<Date,List<IgData>> dayEngagementMap = new HashMap<>();
        instagramInsightRequest.setMetric(InstagramPageEngagementMetric.getListOfMetricAsString());
        LocalDate endDate = LocalDate.now();
        Integer minBackfillDays= isBackFill ? 33 :reportPropertyRepository.findMinDaysForDailySyncForSourceIdAndReportType(
                SocialChannel.INSTAGRAM.getId(),"profile_matric",InstagramPageEngagementMetric.getListOfMetric());
        LocalDate startDate = endDate.minusDays(minBackfillDays);
        for (LocalDate date = startDate; !date.isAfter(endDate.minusDays(1)); date = date.plusDays(1)) {
            Date dayStartDate = Date.from(date.atStartOfDay(ZoneId.of("America/Los_Angeles")).toInstant());
            Date dayEndDate = Date.from(date.atTime(LocalTime.MAX).atZone(ZoneId.of("America/Los_Angeles")).toInstant());
            instagramInsightRequest.setSince(dayStartDate.toInstant().getEpochSecond());
            instagramInsightRequest.setUntil(dayEndDate.toInstant().getEpochSecond());
            List<IgData> instagramResponse = socialProxyHandler.runWithRetryableBirdeyeExceptionWithSingleRetry(() ->
                    instagramExternalApiService.getInstagramAccountInsights(instagramInsightRequest, true));
            dayEngagementMap.put(dayStartDate, instagramResponse);
        }
        return dayEngagementMap;
    }

    private String getIdFromPageLevel(Date date, String instagramAccountId,boolean isQuoteRequired) {
        SimpleDateFormat DATE_FORMATTER = new SimpleDateFormat("yyyy-MM-dd");
        DATE_FORMATTER.setTimeZone(TimeZone.getTimeZone("UTC"));
        String day = DATE_FORMATTER.format(date) + " 00:00:00";
        try {
            if(isQuoteRequired)
                return "\"" + DATE_FORMATTER.parse(day).getTime() +"_" +instagramAccountId + "\"";
            else
                return DATE_FORMATTER.parse(day).getTime() +"_" +instagramAccountId;
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }
    private String getIdFromPageLevelDay(String day, String instagramAccountId) {
        SimpleDateFormat DATE_FORMATTER = new SimpleDateFormat("yyyy-MM-dd");
        DATE_FORMATTER.setTimeZone(TimeZone.getTimeZone("UTC"));
        try {
            return DATE_FORMATTER.parse(day).getTime() +"_" +instagramAccountId;
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    private void calculatePostGainAndEngagementGain(List<PageLevelMetaData> pageLevelMetaDataList, List<PageInsightDataPoint> pageInsightDataPoints,
                                                    Integer mediaCount, Integer currEngagement, Integer followerCount, Integer currVideoViews,
                                                    List<PageInsightDataPoint> pageInsightDataPointsByIds, String pageId, Map<Date,List<IgData>> engagementDataByDay) {
        Integer prevMediaCount = 0;
        Integer totalVideoViews = 0;
        Map<String, PageInsightDataPoint> pageInsightMap = pageInsightDataPointsByIds.stream().collect(Collectors.toMap(
                obj -> getIdFromPageLevelDay(obj.getDate(), pageId), pageInsight -> pageInsight));
        Integer postGain;
        if (CollectionUtils.isNotEmpty(pageInsightDataPoints)) {
            PageInsightDataPoint pageInsightDataPoint = pageInsightDataPoints.get(0);
            prevMediaCount = pageInsightDataPoint.getTotalPosts() == null ? 0 : pageInsightDataPoint.getTotalPosts();
            totalVideoViews = pageInsightDataPoint.getTotalVideoViews() == null ? 0 : pageInsightDataPoint.getTotalVideoViews();
            postGain = mediaCount - prevMediaCount;
        }else{
            postGain = 0; // Inserting data for the first time in ES for given pageId
        }
        Integer followerGain = 0;
        Integer videoViewsGain = totalVideoViews.equals(currVideoViews) ? 0 : currVideoViews - totalVideoViews;
        for (PageLevelMetaData pageLevelMetaData : pageLevelMetaDataList) {
            int engagement = 0, pagePostLikeCount = 0, pagePostCommentCount = 0, pagePostShareCount = 0, pagePostViewCount= 0;
            if (engagementDataByDay.containsKey(pageLevelMetaData.getDate())) {
                List<IgData> engagementData = engagementDataByDay.get(pageLevelMetaData.getDate());
                for (IgData engType : engagementData) {
                    engagement += (Integer) engType.getTotalValue().getValue();
                    switch (engType.getName()) {
                        case "likes":
                            pagePostLikeCount += (Integer) engType.getTotalValue().getValue();
                            break;
                        case "comments":
                            pagePostCommentCount += (Integer) engType.getTotalValue().getValue();
                            break;
                        case "shares":
                            pagePostShareCount += (Integer) engType.getTotalValue().getValue();
                            break;
                        case "views":
                            pagePostViewCount += (Integer) engType.getTotalValue().getValue();
                            break;
                        default:
                            break;
                    }
                }
            }
            if (Objects.nonNull(pageLevelMetaData.getFollowerGainCount()))
                followerGain = pageLevelMetaData.getFollowerGainCount();
            String key = getIdFromPageLevel(pageLevelMetaData.getDate(), pageId, false);
            if (pageInsightMap.containsKey(key)) {
                PageInsightDataPoint dataPoint = pageInsightMap.get(key);
                setPageLeveInfos(Objects.isNull(dataPoint.getTotalPosts()) ? 0 : dataPoint.getTotalPosts(),
                        Objects.isNull(dataPoint.getTotalFollowers()) ? 0 : dataPoint.getTotalFollowers(),
                        Objects.isNull(dataPoint.getTotalVideoViews()) ? 0 : dataPoint.getTotalVideoViews(),
                        pageLevelMetaData,(engagement == 0) ? dataPoint.getPostEngagements() : engagement,
                        Objects.isNull(dataPoint.getPostCount()) ? 0 : dataPoint.getPostCount(),
                        Objects.isNull(dataPoint.getPostEngagementTotal()) ? 0 : dataPoint.getPostEngagementTotal(),
                        followerGain > 0 ? followerGain : (Objects.isNull(dataPoint.getFollowersGain()) ? 0 : dataPoint.getFollowersGain()),
                       // Objects.isNull(dataPoint.getFollowersGain()) ? 0 : dataPoint.getFollowersGain(),
                        Objects.isNull(dataPoint.getVideoViews()) ? 0 : dataPoint.getVideoViews(),
                        pagePostLikeCount, pagePostCommentCount, pagePostShareCount, pagePostViewCount);
            } else
                setPageLeveInfos(mediaCount, followerCount, currVideoViews, pageLevelMetaData,
                        engagement, postGain, currEngagement, followerGain, videoViewsGain,
                        pagePostLikeCount, pagePostCommentCount, pagePostShareCount, pagePostViewCount);
        }
    }

    private void setPageLeveInfos(Integer mediaCount, Integer followerCount, Integer currVideoViews,
                                  PageLevelMetaData pageLevelMetaData, Integer engagementGain, Integer postGain,
                                  Integer engagementTotalGain, Integer followerGain, Integer videoViewsGain,
                                  Integer pagePostLikeCount, Integer pagePostCommentCount, Integer pagePostShareCount, Integer pagePostViewCount) {
        pageLevelMetaData.setPostEngagements(engagementGain);
        pageLevelMetaData.setPostTotalCount(mediaCount);
        pageLevelMetaData.setPostCount(postGain >= 0 ? postGain : 0);
        pageLevelMetaData.setTotalFollower(followerCount);
        pageLevelMetaData.setPostEngagementTotal(engagementTotalGain);
        pageLevelMetaData.setFollowerGainCount(followerGain >= 0 ? followerGain : 0);
        pageLevelMetaData.setProfileVideoViews(videoViewsGain >= 0 ? videoViewsGain : 0);
        pageLevelMetaData.setTotalProfileVideoViews(currVideoViews);
        pageLevelMetaData.setPagePostLikeCount(pagePostLikeCount);
        pageLevelMetaData.setCommentCount(pagePostCommentCount);
        pageLevelMetaData.setShareCount(pagePostShareCount);
        pageLevelMetaData.setPostImpressions(pagePostViewCount);
    }

    @Override
    public void addInsightsToDB(PageInsights pageInsights) {
        InstagramPageInsight instagramPageInsight = dbDataConverter.convertPageInsightForInstagram(pageInsights);
        instagramPageInsightRepo.save(instagramPageInsight);
    }


    private void calculateImpressionAndReach(PageLevelMetaData pageLevelMetaData) {
        pageLevelMetaData.setPostEngagements(0);
        pageLevelMetaData.setPostTotalCount(0);
        pageLevelMetaData.setPostCount(0);
        pageLevelMetaData.setTotalFollower(0);
        pageLevelMetaData.setPostEngagementTotal(0);
        pageLevelMetaData.setFollowerGainCount(0);
    }

    private void processIgBackFill(BusinessInstagramAccount igAccount, PageLevelMetaData pageLevelMetaData) {
        calculateImpressionAndReach(pageLevelMetaData);

        PageInsights pageInsights = new PageInsights(igAccount.getEnterpriseId(), igAccount.getInstagramAccountId(), igAccount.getBusinessId(),
                SocialChannel.INSTAGRAM.getName(), Collections.singletonList(pageLevelMetaData));

        kafkaProducerService.sendObjectV1(Constants.INSTAGRAM_PAGE_INSIGHTS, pageInsights);

        addInsightsToDB(pageInsights);
    }
    private List<PageLevelMetaData> getInsightForIgAccount(BusinessInstagramAccount igAccount, InstagramInsightRequest instagramInsightRequest) {
        List<IgData> instagramResponse = socialProxyHandler.runWithRetryableBirdeyeExceptionWithSingleRetry(() ->
                instagramExternalApiService.getInstagramAccountInsights(instagramInsightRequest, false));

        LOG.info("Ig insight response for ig account id: {} : {}",igAccount.getInstagramAccountId(),instagramResponse);

        return reportDataConverter.prepareDayWiseDataForInstagram(instagramResponse, igAccount.getInstagramAccountId(),
                igAccount.getBusinessId());
    }
    @Override
    public void backfillIgInsight(BackfillInsightReq socialScanEventDTO) {
        Integer minBackfillDays= reportPropertyRepository.findMinDaysForSourceIdAndReportType(
                socialScanEventDTO.getSourceId(),"profile_matric");
        if(InsightsReportUtil.validateStartAndEndDate(socialScanEventDTO.getStartDate(),socialScanEventDTO.getEndDate(),minBackfillDays)) {
            BusinessInstagramAccount igAccount = businessInstagramAccountRepository.findByInstagramAccountId(socialScanEventDTO.getExternalId());
            long diff = socialScanEventDTO.getEndDate().getTime() - socialScanEventDTO.getStartDate().getTime();
            int daysLeft = (int) TimeUnit.DAYS.convert(diff, TimeUnit.MILLISECONDS);
            Date initialDate = socialScanEventDTO.getStartDate();
            List<PageLevelMetaData> pageLevelMetaDataList = new ArrayList<>();
            do {
                if (daysLeft <= 30) {
                    InstagramInsightRequest instagramInsightRequest = reportDataConverter.createInstagramRequest(igAccount, socialScanEventDTO.getEndDate(), initialDate);
                    instagramInsightRequest.setMetric(InstagramPageInsightMetric.reach.name());
                    pageLevelMetaDataList.addAll(getInsightForIgAccount(igAccount, instagramInsightRequest));
                    pageLevelMetaDataList.forEach( pageLevelMetaData-> processIgBackFill(igAccount,pageLevelMetaData));
                    getFollowerCount(igAccount,pageLevelMetaDataList,instagramInsightRequest,socialScanEventDTO);
                    return;
                }
                Date newEndDate = DateUtils.addDays(initialDate, 30);
                InstagramInsightRequest instagramInsightRequest = reportDataConverter.createInstagramRequest(igAccount, newEndDate, initialDate);
                instagramInsightRequest.setMetric(InstagramPageInsightMetric.reach.name());
                pageLevelMetaDataList.addAll(getInsightForIgAccount(igAccount, instagramInsightRequest));
                initialDate = newEndDate;
                daysLeft -= 30;
            } while (true);
        }
    }

    private void getFollowerCount(BusinessInstagramAccount igAccount, List<PageLevelMetaData> pageLevelMetaDataList, InstagramInsightRequest instagramInsightRequest, BackfillInsightReq socialScanEventDTO) {
        Date endDate = socialScanEventDTO.getEndDate();
        Date startDate = socialScanEventDTO.getStartDate();
        Date thirtyDaysAgo = DateUtils.addDays(new Date(), -30);
        if (endDate.before(thirtyDaysAgo))
            return;
        if (!startDate.after(thirtyDaysAgo))
            startDate = thirtyDaysAgo;
        instagramInsightRequest.setUntil(endDate.toInstant().getEpochSecond());
        instagramInsightRequest.setSince(startDate.toInstant().getEpochSecond());
        instagramInsightRequest.setMetric(InstagramPageInsightMetric.follower_count.name());
        List<PageLevelMetaData> followerPageLevelMetaData = getInsightForIgAccount(igAccount, instagramInsightRequest);
        Map<Date, PageLevelMetaData> pageLevelMetaDataMap = pageLevelMetaDataList.stream().collect(Collectors.toMap(
                PageLevelMetaData::getDate, metaData -> metaData
        ));
        followerPageLevelMetaData.forEach(pageLevelMetaData -> {
            if (pageLevelMetaDataMap.containsKey(pageLevelMetaData.getDate())) {
                PageLevelMetaData metaData = pageLevelMetaDataMap.get(pageLevelMetaData.getDate());
                metaData.setFollowerGainCount(pageLevelMetaData.getFollowerGainCount());
            }
        });
    }
    @Override
    public void backFillIgEngagement(IgBackFillInsightReq igBackFillInsightReq) {
        List<InstagramPageInsight> pageInsights = instagramPageInsightRepo.findByPageId(igBackFillInsightReq.getPageId());
        if(CollectionUtils.isNotEmpty(pageInsights)){
            InstagramPageInsight firstPageInsight = pageInsights.get(0);
            PageInsights firstInsightData = JSONUtils.fromJSON(firstPageInsight.getData(), PageInsights.class);
            int prevEngagement = 0;
            if(Objects.nonNull(firstInsightData)) {
                int tempEngagement = firstInsightData.getPageInsights().get(0).getPostEngagements();
                int totalPostEngagement = firstInsightData.getPageInsights().get(0).getPostEngagementTotal();
                firstInsightData.getPageInsights().get(0).setPostEngagements(tempEngagement - totalPostEngagement);
                firstInsightData.getPageInsights().get(0).setPostEngagementTotal(tempEngagement);
                prevEngagement = tempEngagement;
                firstPageInsight.setData(JSONUtils.toJSON(firstInsightData));
                pageInsights.set(0,firstPageInsight);
                kafkaProducerService.sendObjectV1(Constants.INSTAGRAM_PAGE_INSIGHTS+"-v2", firstInsightData);
            }
            for (int i = 1; i < pageInsights.size();i++) {
                InstagramPageInsight currPageInsight = pageInsights.get(i);
                InstagramPageInsight prevPageInsight = pageInsights.get(i - 1);
                PageInsights currInsightData = JSONUtils.fromJSON(currPageInsight.getData(), PageInsights.class);
                PageInsights prevInsightData = JSONUtils.fromJSON(prevPageInsight.getData(), PageInsights.class);
                if (Objects.nonNull(currInsightData) && Objects.nonNull(prevInsightData)) {
                    if (CollectionUtils.isNotEmpty(currInsightData.getPageInsights()) && currInsightData.getPageInsights().size() == 1) {
                        int currTotal = currInsightData.getPageInsights().get(0).getPostEngagements() +prevEngagement;
                        int currEngagement = currTotal - prevInsightData.getPageInsights().get(0).getPostEngagementTotal();
                        prevEngagement = currInsightData.getPageInsights().get(0).getPostEngagements();
                        currInsightData.getPageInsights().get(0).setPostEngagementTotal(currTotal);
                        currInsightData.getPageInsights().get(0).setPostEngagements(currEngagement);
                        currPageInsight.setData(JSONUtils.toJSON(currInsightData));
                        pageInsights.set(i,currPageInsight);
                    }
                }
                kafkaProducerService.sendObjectV1(Constants.INSTAGRAM_PAGE_INSIGHTS+"-v2", currInsightData);
            }
        }
    }

    @Override
    public void backFillIgEngagementInit() {
        List<String> pageIds = instagramPageInsightRepo.findPageIds();
        for (String pageId: pageIds) {
            LOG.info("Starting engagement data correction for {}",pageId);
            kafkaProducerService.sendObjectV1(Constants.INSTAGRAM_PAGE_INSIGHTS_CORRECTIONS, new IgBackFillInsightReq(null,pageId));
        }
    }

    @Override
    public void backfillEngagmentData(BackfillRequest backfillRequest) {
        BoolQueryBuilder boolQueryBuilder = reportsEsService.prepareQueryToGetData(backfillRequest,
                backfillRequest.getBusinessInstagramAccount().getInstagramAccountId());
        String index = ElasticConstants.INSTAGRAM_PAGE_INSIGHTS.getName();
        int size = 30;
        List<ESPageRequest> esPageRequests = reportsEsService.getDataFromEsIndex(boolQueryBuilder,size,index);
        if(CollectionUtils.isEmpty(esPageRequests)){
            LOG.info("No data to migrate for request :{}",backfillRequest);
            return;
        }
        Map<String,ESPageRequest> esPageRequestMap = esPageRequests.stream().collect(Collectors.toMap(ESPageRequest::getDay, Function.identity()));
        LOG.info("esPageRequestMap :{}",esPageRequestMap);
        InstagramInsightRequest instagramInsightRequest = reportDataConverter.createInstagramRequest(backfillRequest.getBusinessInstagramAccount()
                , backfillRequest.getBackfillInsightReq().getEndDate(), backfillRequest.getBackfillInsightReq().getStartDate());
        Map<Date,List<IgData>> getEngagementDataMap = getEngagementDataMap(instagramInsightRequest,true);
        LOG.info("getEngagementDataMap :{}",getEngagementDataMap);
        if(MapUtils.isEmpty(getEngagementDataMap)){
            LOG.info("No data to migrate for request :{}",backfillRequest);
            return;
        }
        List<ESPageRequest> finalResponse = new ArrayList<>();
        for(Date d : getEngagementDataMap.keySet()){
            ESPageRequest esPageRequest = esPageRequestMap.get(DateTimeUtils.localToESFormat(d,"yyyy-MM-dd 00:00:00"));
            if(Objects.isNull(esPageRequest)){
                LOG.info("No ES page request found for date: {}",DateTimeUtils.localToESFormat(d,"yyyy-MM-dd 00:00:00"));
                continue;
            }
            LOG.info("Data found for date :{}",DateTimeUtils.localToESFormat(d,"yyyy-MM-dd 00:00:00"));
            int pagePostLikeCount = 0, pagePostCommentCount = 0, pagePostShareCount = 0,engagement = 0, pagePostViewCount = 0;
            List<IgData> igDataList = getEngagementDataMap.get(d);
            for (IgData engType : igDataList) {
                engagement += (int)engType.getTotalValue().getValue();
                switch (engType.getName()) {
                    case "likes":
                        pagePostLikeCount += (Integer) engType.getTotalValue().getValue();
                        break;
                    case "comments":
                        pagePostCommentCount += (Integer) engType.getTotalValue().getValue();
                        break;
                    case "shares":
                        pagePostShareCount += (Integer) engType.getTotalValue().getValue();
                        break;
                    case "views":
                        pagePostViewCount += (Integer) engType.getTotalValue().getValue();
                        break;
                    default:
                        break;
                }
            }
            esPageRequest.setPost_comment_count(pagePostCommentCount);
            esPageRequest.setPost_like_count(pagePostLikeCount);
            esPageRequest.setPost_share_count(pagePostShareCount);
            esPageRequest.setPost_engagement(engagement);
            esPageRequest.setPost_impressions(pagePostViewCount);
            finalResponse.add(esPageRequest);
        }
        reportsEsService.bulkPostPageInsights(finalResponse,index);
    }

}
