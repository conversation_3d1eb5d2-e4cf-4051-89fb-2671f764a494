package com.birdeye.social.service.SocialReportService.Instagram;

import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.constant.*;
import com.birdeye.social.dao.BusinessInstagramAccountRepository;
import com.birdeye.social.dao.SocialPostRepository;
import com.birdeye.social.dao.reports.BusinessPostsRepository;
import com.birdeye.social.dao.reports.PostInsightAuditRepo;
import com.birdeye.social.dto.BusinessBizLiteDto;
import com.birdeye.social.dto.BusinessCoreUser;
import com.birdeye.social.dto.EsPostDataPoint;
import com.birdeye.social.dto.PostInsightDTO;
import com.birdeye.social.dto.report.SocialScanEventDTO;
import com.birdeye.social.entities.BusinessInstagramAccount;
import com.birdeye.social.entities.SocialPost;
import com.birdeye.social.entities.report.BusinessPosts;
import com.birdeye.social.entities.report.PostInsightAudit;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.insights.ES.*;
import com.birdeye.social.insights.ES.Request.ESPageRequest;
import com.birdeye.social.insights.ES.Request.InsightsESRequest;
import com.birdeye.social.insights.Facebook.PostDataAndInsightResponse;
import com.birdeye.social.insights.*;
import com.birdeye.social.insights.Instagram.ExternalApiResponse.IgData;
import com.birdeye.social.insights.Instagram.ExternalApiResponse.InstagramAccountDataResponse;
import com.birdeye.social.insights.Instagram.InstagramPostInsightRequest;
import com.birdeye.social.insights.constants.ElasticConstants;
import com.birdeye.social.insights.constants.GroupByType;
import com.birdeye.social.insights.constants.InsightsConstants;
import com.birdeye.social.model.BackfillInsightReq;
import com.birdeye.social.model.BackfillRequest;
import com.birdeye.social.service.CommonService;
import com.birdeye.social.service.InstagramSocialService;
import com.birdeye.social.service.SocialBusinessPropertyService;
import com.birdeye.social.service.SocialReportService.Converter.ReportDataConverter;
import com.birdeye.social.service.SocialReportService.ES.ReportsEsService;
import com.birdeye.social.service.SocialReportService.ExternalService.InstagramExternalApiService;
import com.birdeye.social.service.SocialTagService;
import com.birdeye.social.utils.JSONUtils;
import com.birdeye.social.utils.SocialProxyHandler;
import com.birdeye.social.utils.StringUtils;
import com.birdeye.social.utils.TopPostsReportUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.search.sort.SortOrder;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpStatusCodeException;

import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static com.birdeye.social.constant.Constants.BATCH_SIZE_FOR_TOP_POSTS;
import static com.birdeye.social.constant.Constants.THREADPOOL_SIZE_FOR_TOP_POSTS;
import static com.birdeye.social.insights.constants.InsightsConstants.IG_ENGAGE_SYNC_EVENT;

@Service
public class InstagramInsightsImpl implements InstagramInsights{

    @Autowired
    private ReportsEsService reportsEsService;

    @Autowired
    private ReportDataConverter reportDataConverter;

    @Autowired
    private BusinessInstagramAccountRepository businessInstagramAccountRepository;

    @Autowired
    private InstagramExternalApiService instagramExternalApiService;

    @Autowired
    private BusinessPostsRepository businessPostsRepository;

    @Autowired
    private KafkaProducerService kafkaProducerService;

    @Autowired
    private PostInsightAuditRepo postInsightAuditRepo;

    @Autowired
    private SocialProxyHandler socialProxyHandler;
    @Autowired
    private CommonService commonService;

    @Autowired
    private InstagramSocialService instagramSocialService;

    @Autowired
    private SocialTagService socialTagService;

    @Autowired
    private SocialBusinessPropertyService socialBusinessPropertyService;

    @Autowired
    private IBusinessCoreService coreService;

    @Autowired
    private SocialPostRepository socialPostRepository;

    private static final Logger log = LoggerFactory.getLogger(InstagramInsightsImpl.class);

    private static final String SOCIAL_PAGE_SCAN_EVENT = "page_scanned";
    private static final String SOCIAL_BACKFILL_PAGE_SCAN_EVENT = "instagram_backfill_page_scanned";

    @Override
    public PageInsightsResponse getInstagramInsightsForPage(InsightsRequest insights) throws Exception {
        log.info("Request to get insight for pages from business ids with enterprise id: {}", insights.getEnterpriseId());
        String index = ElasticConstants.INSTAGRAM_PAGE_INSIGHTS.getName();
        List<String> pageIds = getPageIdsByBids(insights.getBusinessIds());
        if(CollectionUtils.isEmpty(pageIds)){
            return new PageInsightsResponse();
        }
        Date queryStartDate = insights.getStartDate();
        String startDate  = reportsEsService.getStartDate(pageIds,index);
        insights.setStartDate(reportDataConverter.getStartDate(insights,insights.getBusinessIds(),startDate));
        if(SearchTemplate.PAGE_POST_ENGAGEMENT.getName().equalsIgnoreCase(insights.getReportType())) {
            insights.setStartDate(reportsEsService.getFirstPostDate(insights, pageIds, queryStartDate));
        }
        long diff = insights.getEndDate().getTime() - insights.getStartDate().getTime();
        long noOfDaysBetween = TimeUnit.DAYS.convert(diff, TimeUnit.MILLISECONDS);
        reportDataConverter.setGroupByType(insights,noOfDaysBetween);
        InsightsESRequest request = reportDataConverter.createESRequestForPage(insights,index,pageIds, SocialChannel.INSTAGRAM.getId());
        PageInsightEsData pageInsightDataFromEs = reportsEsService.getPageInsightDataFromEs(request);
        reportsEsService.updatePostCountForDates(request, pageInsightDataFromEs, pageIds, queryStartDate);
        GroupByType type =  GroupByType.getByName(insights.getGroupByType());
        log.info("Fetched insight from es for pages");
        PageInsightsResponse pageInsightsResponse = reportDataConverter.prepareFBPageInsightResponse(pageInsightDataFromEs, SearchTemplate.searchTemplate(insights.getReportType()));
        List<PageInsightDataPoint> dataPoints = pageInsightDataFromEs.getPointsList();
        pageInsightsResponse.setDateDiff(reportsEsService.getTimeDifference(request));
        pageInsightsResponse.setGroupByType(type.getType());
        if(CollectionUtils.isNotEmpty(dataPoints)){
            reportDataConverter.customizedDataForFirstIndex(insights.getEndDate(),type,pageInsightsResponse,dataPoints);
        }
        pageInsightsResponse.setDataPoints(dataPoints);
        return pageInsightsResponse;
    }
    @Override
    public PageInsightV2EsData getPageInsightsESData(InsightsRequest insights) throws Exception {
        log.info("getPageInsightsESData Request to get insight for pages from business ids with enterprise id: {}", insights.getEnterpriseId());
        String index = ElasticConstants.INSTAGRAM_PAGE_INSIGHTS.getName();
        List<String> pageIds = getPageIdsByBids(insights.getBusinessIds());
        if(CollectionUtils.isEmpty(pageIds)){
            return new PageInsightV2EsData();
        }

        long diff = insights.getEndDate().getTime() - insights.getStartDate().getTime();
        long noOfDaysBetween = TimeUnit.DAYS.convert(diff, TimeUnit.MILLISECONDS);
        reportDataConverter.setGroupByType(insights,noOfDaysBetween);


        InsightsESRequest request = reportDataConverter.createESRequestForPage(insights,index,pageIds, SocialChannel.INSTAGRAM.getId());
        PageInsightV2EsData pageInsightDataFromEs = reportsEsService.getPageInsightDataFromEsChannelWise(request);
        if(SearchTemplate.PAGE_FOLLOWER_INSIGHTS.getName().equalsIgnoreCase(insights.getReportType())){
            request.setSearchTemplate(SearchTemplate.PAGE_FOLLOWER_TOTAL_INSIGHTS);
            pageInsightDataFromEs = reportsEsService.getTotalAudFromEsChannelWise(request, pageInsightDataFromEs);
        }
        //TODO Khushboo to check reportsEsService.updatePostCountForDates(request, pageInsightDataFromEs, pageIds, queryStartDate);
        pageInsightDataFromEs.setDateDiff(reportsEsService.getTimeDifference(request));

        return pageInsightDataFromEs;
    }

    @Override
    public PageInsightV2EsData getInstagramInsightsForMessageSent(InsightsRequest insights) throws Exception {
        log.info("getPageInsightsESData Request to get insight for pages from business ids with enterprise id: {}", insights.getEnterpriseId());
        List<String> pageIds = getPageIdsByBids(insights.getBusinessIds());
        if(CollectionUtils.isEmpty(pageIds)){
            return new PageInsightV2EsData();
        }

        long diff = insights.getEndDate().getTime() - insights.getStartDate().getTime();
        long noOfDaysBetween = TimeUnit.DAYS.convert(diff, TimeUnit.MILLISECONDS);
        reportDataConverter.setGroupByType(insights,noOfDaysBetween);


        List<String> sentFeedType = Arrays.asList(EngageV2FeedTypeEnum.COMMENT.name());
        List<String> receiveFeedType = Arrays.asList(EngageV2FeedTypeEnum.MEDIA_TAGS.name(), EngageV2FeedTypeEnum.COMMENT.name());
        List<String> receiveSubFeedType = Arrays.asList(EngageV2FeedSubTypeEnum.MENTION.name());

        InsightsESRequest request = reportDataConverter.createESRequestForMessage(insights, ElasticConstants.SOCIAL_ENGAGE_V2_FEED.getName(),
                pageIds, SocialChannel.INSTAGRAM.getId(), sentFeedType, null, receiveFeedType, receiveSubFeedType);


        request.setSearchTemplate(SearchTemplate.PPR_PAGE_POST_MESSAGE_METRIC);
        PageInsightV2EsData pageInsightDataFromEs = new PageInsightV2EsData();
        pageInsightDataFromEs = reportsEsService.getPageInsightDataFromEsChannelWise(request);
        pageInsightDataFromEs.setDateDiff(reportsEsService.getFeedTimeDifference(request));

        return pageInsightDataFromEs;
    }

    @Override
    public PageInsightV2EsData getInstagramInsightsForPublishPost(InsightsRequest insights) throws Exception {
        log.info("Request to get insight for pages from business ids with enterprise id: {}", insights.getEnterpriseId());
        String index = ElasticConstants.POST_INSIGHTS.getName();
        List<String> pageIds = getPageIdsByBids(insights.getBusinessIds());
        if(CollectionUtils.isEmpty(pageIds)){
            return new PageInsightV2EsData();
        }
        long diff = insights.getEndDate().getTime() - insights.getStartDate().getTime();
        long noOfDaysBetween = TimeUnit.DAYS.convert(diff, TimeUnit.MILLISECONDS);
        reportDataConverter.setGroupByType(insights,noOfDaysBetween);
        InsightsESRequest request = reportDataConverter.createESRequestForPage(insights,index,pageIds, SocialChannel.INSTAGRAM.getId());
        request.setSearchTemplate(SearchTemplate.PPR_PAGE_POST_PUBLISHED_METRIC);
        PageInsightV2EsData pageInsightDataFromEs = reportsEsService.getPageInsightDataFromEsChannelWise(request);
        pageInsightDataFromEs.setDateDiff(reportsEsService.getTimeDifference(request));
        log.info("Fetched insight from es for pages");

        return pageInsightDataFromEs;
    }


    @Override
    public PostDataAndInsightResponse getInstagramInsightsForPost(InsightsRequest insights, int startIndex, int pageSize, String sortParam, String sortOrder, boolean excelDownload) {
        log.info("Request to get post insight with enterprise id: {}", insights.getEnterpriseId());
        PostDataAndInsightResponse response = new PostDataAndInsightResponse();
        InsightsPostRequest postRequest = new InsightsPostRequest(insights,startIndex,pageSize,sortParam,sortOrder,Integer.toString(SocialChannel.INSTAGRAM.getId()),getPageIdsByBids(insights.getBusinessIds()));
        InsightsESRequest request = reportDataConverter.createESRequestForPost(postRequest,ElasticConstants.POST_INSIGHTS.getName());
        log.info("InsightsESRequest with page ids : {}",request.getDataModel());
        return reportsEsService.getPostDataFromEs(request, excelDownload);
    }

    @Override
    public void getPostInsights(BusinessPosts businessPosts, Boolean isFreshRequest) {
        BusinessInstagramAccount businessInstagramAccount = businessInstagramAccountRepository.findByInstagramAccountId(businessPosts.getExternalPageId());
        if(Objects.isNull(businessInstagramAccount)) {
            log.info("No data found for page id: {}",businessPosts.getExternalPageId());
            return;
        }
        if(businessPosts.getIsDeleted().equals(1) ||
        !instagramSocialService.getInstagramPostPermission(Collections.singletonList(businessInstagramAccount), Collections.singletonList("REPORT"))){
            log.info("Is valid is 0 for page id: {} or post id deleted for post id : {}", businessPosts.getExternalPageId(),businessPosts.getPostId());
            return;
        }
        if(Objects.nonNull(businessInstagramAccount.getEnterpriseId()) && !socialBusinessPropertyService.isSocialPostingAndReportingEnabled(businessInstagramAccount.getEnterpriseId())){
            log.info("Social reporting is not enabled for enterprise id : {}", businessInstagramAccount.getEnterpriseId());
            return;
        }
        try {
            boolean isReel = "reel".equals(businessPosts.getPostType());
            boolean isStory = "story".equals(businessPosts.getPostType());

            InstagramPostInsightRequest instagramPostInsightRequest = reportDataConverter.createInstagramPostRequest(businessInstagramAccount, businessPosts.getPostId(),isReel, businessPosts.getPostUrl(), isStory);
            List<IgData> igDataList = socialProxyHandler.runWithRetryableBirdeyeExceptionWithSingleRetry(() ->
                    instagramExternalApiService.getInstagramPostInsights(instagramPostInsightRequest));

            InstagramAccountDataResponse engagementResponse =socialProxyHandler.runWithRetryableBirdeyeExceptionWithSingleRetry(() ->
                    instagramExternalApiService.getInstagramEngagementData(businessPosts.getPostId(), businessInstagramAccount.getPageAccessToken()));
            log.info("IG postInsights data : {}", igDataList);
            PostInsightDTO postInsightDTO = convertIgDataToPostInsightData(igDataList, engagementResponse,isReel);
            PostData postData = createPostData(businessPosts, postInsightDTO, engagementResponse);
            ObjectMapper objectMapper = new ObjectMapper();
            String postInsightJson = objectMapper.writeValueAsString(postInsightDTO);
            businessPosts.setResponse(postInsightJson);
            businessPostsRepository.saveAndFlush(businessPosts);
            auditPostInsight(businessPosts, false);
            // Send Event to sync post and its comments engagement in social_engage_feed
            sendSocialEngageSyncEvent(postData);
            if(!isFreshRequest || (CollectionUtils.isEmpty(postData.getImages()) && CollectionUtils.isEmpty(postData.getVideos()))) {
                sendPostDataToKafka(postData);
            } else {
                commonService.uploadMediaToPicturesque(postData, businessPosts);
                saveCDNPostToES(businessPosts);
            }
        }catch (HttpStatusCodeException ex){
            businessPosts.setIsDeleted(1);
            businessPosts.setError(ex.getMessage());
            businessPostsRepository.saveAndFlush(businessPosts);
            auditPostInsight(businessPosts, true);
            log.info("HttpStatusCodeException occurred while getting data for postId: {} of IG page: {}  with error {}",businessPosts.getPostId(),businessPosts.getExternalPageId(), ex);
        }catch (Exception e) {
            businessPosts.setError(e.getMessage());
            businessPostsRepository.saveAndFlush(businessPosts);
            auditPostInsight(businessPosts, true);
            log.info("Exception occurred while getting data for postId: {} of IG page: {} with error {}",businessPosts.getPostId(),businessPosts.getExternalPageId(), e);
        }


    }

    private void addPostAuthorDetails(BusinessPosts businessPosts, PostData postData) {
        boolean isBePost = Objects.equals(postData.getSourceId(), SocialChannel.APPLE_CONNECT.getId()) ||
                Objects.nonNull(postData.getBePostId()) && !Objects.equals(postData.getPostId(), postData.getBePostId());
        // Check if the author details are missing in BusinessPosts
        boolean isAuthorDetailsMissing = Objects.isNull(businessPosts.getCreatedByBeUserId())
                || Objects.isNull(businessPosts.getCreatedByBeUserName())
                || Objects.isNull(businessPosts.getCreatedByBeUserEmail());

        BusinessCoreUser businessCoreUser = null;

        if (isBePost && isAuthorDetailsMissing) {
            businessCoreUser = getBusinessCoreUser(businessPosts);
        }

        // Update BusinessPosts with the author details
        if (isAuthorDetailsMissing && Objects.nonNull(businessCoreUser)) {
            businessPosts.setCreatedByBeUserId(businessCoreUser.getId());
            businessPosts.setCreatedByBeUserName(businessCoreUser.getName());
            businessPosts.setCreatedByBeUserEmail(businessCoreUser.getEmailId());
        }

        // Set author details in postData which is later updated in ES
        postData.setPublisherId(Objects.nonNull(businessCoreUser) ? businessCoreUser.getId() : businessPosts.getCreatedByBeUserId());
        postData.setPublisherName(Objects.nonNull(businessCoreUser) ? businessCoreUser.getName() : businessPosts.getCreatedByBeUserName());
        postData.setPublisherEmail(Objects.nonNull(businessCoreUser) ? businessCoreUser.getEmailId() : businessPosts.getCreatedByBeUserEmail());
    }

    private BusinessCoreUser getBusinessCoreUser(BusinessPosts businessPosts) {
        Integer createdById = getCreatedById(businessPosts);
        // createdById is 0 for PUBLIC-POSTING
        if (Objects.nonNull(createdById) && createdById != 0) {
            return coreService.getUserInfo(createdById);
        }
        return null;
    }

    private Integer getCreatedById(BusinessPosts businessPosts) {
        if (Objects.isNull(businessPosts.getCreatedByBeUserId())) {
            SocialPost socialPost = socialPostRepository.findById(businessPosts.getBePostId());
            if (Objects.nonNull(socialPost) && Objects.nonNull(socialPost.getCreatedBy())) {
                return socialPost.getCreatedBy();
            }
        }
        return businessPosts.getCreatedByBeUserId();
    }

    @Override
    public void postInstagramPageInsightToES(PageInsights pageInsights) {
        String index = InsightsConstants.INSTAGRAM_PAGE_INSIGHTS;
        log.info("Started es upload for business id : {}",pageInsights.getBusinessId());
        List<ESPageRequest> esPageRequest = reportDataConverter.createPageInsightsObject(pageInsights);
        reportsEsService.bulkPostPageInsights(esPageRequest,index);
        log.info("Executed es upload for business id :{}",pageInsights.getBusinessId());
    }

    @Override
    public void postInstagramPostInsightsToEs(PostData postData) {
        String index = InsightsConstants.POST_INSIGHT;
        List<EsPostDataPoint> esPostDataPoints = reportDataConverter.preparePostEsDTO(postData);
        reportsEsService.bulkPostPagePostDataToES(esPostDataPoints,index);
    }

    @Override
    public void updateInstagramPageInsightsPostCount(PageInsights pageInsights) {
        String index = InsightsConstants.INSTAGRAM_PAGE_INSIGHTS;

    }

    @Override
    public void startScanForPosts(String pageId) {
        BusinessInstagramAccount igAccount = businessInstagramAccountRepository.findByInstagramAccountId(pageId);
        if(Objects.isNull(igAccount)) {
            return;
        }

        SocialScanEventDTO scanEventDTO = new SocialScanEventDTO();
        scanEventDTO.setSourceId(SocialChannel.INSTAGRAM.getId());
        scanEventDTO.setSourceName(SocialChannel.INSTAGRAM.getName());
        scanEventDTO.setChannelPrimaryId(igAccount.getId());
        scanEventDTO.setBusinessId(igAccount.getBusinessId());
        scanEventDTO.setEnterpriseId(igAccount.getEnterpriseId());
        scanEventDTO.setExternalId(igAccount.getInstagramAccountId());
        scanEventDTO.setPageName(igAccount.getInstagramAccountName());

        kafkaProducerService.sendObjectV1(scanEventDTO.getSourceName().concat("_").concat(SOCIAL_PAGE_SCAN_EVENT), scanEventDTO);
    }

    private void sendPostDataToKafka(PostData postData) {
        String key = postData.getPostId().concat("_").concat(postData.getPostingDate().toString());
        kafkaProducerService.sendObjectV1(InsightsConstants.IG_POST_INSIGHT_TOPIC, postData);
    }
    private boolean sendSocialEngageSyncEvent(PostData postData) {
        return kafkaProducerService.sendObjectV1(IG_ENGAGE_SYNC_EVENT, postData);
    }

    @Override
    public PostData createPostData(BusinessPosts businessPosts, PostInsightDTO postInsightDTO, InstagramAccountDataResponse engagementResponse) {
        List<String> media;
        PostData postData = new PostData();
        postData.setId(businessPosts.getId());
        postData.setBePostId(businessPosts.getBePostId()!=null?businessPosts.getBePostId().toString()
                :null);
        postData.setPostId(businessPosts.getPostId());
        postData.setSourceId(businessPosts.getSourceId());
        postData.setPageName(businessPosts.getPageName());
        postData.setPageId(businessPosts.getExternalPageId());
        postData.setBusinessId(businessPosts.getBusinessId());
        postData.setEnterpriseId(businessPosts.getEnterpriseId());
        postData.setPostUrl(businessPosts.getPostUrl());
        postData.setPostText(businessPosts.getPostText());
        postData.setPostingDate(businessPosts.getPublishDate());
        postData.setPostType(businessPosts.getPostType());
        if(StringUtils.isNotEmpty(businessPosts.getImageUrls())) {
            media = Arrays.asList(businessPosts.getImageUrls().split(","));
            postData.setImages(media);
        }
        if( StringUtils.isNotEmpty(businessPosts.getVideoUrls())) {
            media = Arrays.asList(businessPosts.getVideoUrls().split(","));
            postData.setVideos(media);
        }
        postData.setEngagement(Objects.isNull(postInsightDTO.getEngagement()) ? 0 : postInsightDTO.getEngagement());
        postData.setImpression(Objects.isNull(postInsightDTO.getImpression()) ? 0 : postInsightDTO.getImpression());
        postData.setReach(postInsightDTO.getReach());

        commonService.prepareDeltaInsightsForPost(postData,businessPosts);
        if(Objects.nonNull(postData.getEngagement()) && Objects.nonNull(postData.getReach()) && postData.getReach()!=0) {
            postData.setEngagementRate((double)postData.getEngagement()/postData.getReach());
        }

        if(Objects.nonNull(businessPosts.getBePostId())) {
            postData.setTagIds(socialTagService.getTagIdsFromEntityId(Long.valueOf(businessPosts.getBePostId()), SocialTagEntityType.POST));
        }

//        postData.setVideoViews(postInsightDTO.getVideoViews());

        postData.setVideoViews(getMediaVideoViews(businessPosts, postInsightDTO));

        // add post author details to be shown on analyze tab
        addPostAuthorDetails(businessPosts, postData);
        if(Objects.nonNull(engagementResponse)) {
            postData.setLikeCount(engagementResponse.getLikeCount());
            postData.setCommentCount(engagementResponse.getCommentCount());
            postData.setShareCount(0);
        }
        return postData;
    }

    private Integer getMediaVideoViews(BusinessPosts businessPosts, PostInsightDTO postInsightDTO) {

        if(Objects.nonNull(postInsightDTO.getVideoViews())) {
            return postInsightDTO.getVideoViews();
        }

        PostInsightDTO oldPostInsight = null;
        try {
            if (Objects.nonNull(businessPosts.getResponse())) {
                oldPostInsight = JSONUtils.fromJSON(businessPosts.getResponse(), PostInsightDTO.class);
                return oldPostInsight.getVideoViews();
            } return null;
        } catch (Exception e) {
            log.info("Exception while converting insight response for: {} {}: {}", businessPosts.getId(),businessPosts.getResponse(),e.getMessage());
            return null;
        }
    }

    @Override
    public void backfillEngagmentBreakDown(BackfillInsightReq request) {
        List<BusinessInstagramAccount> businessInstagramAccounts;
        if(request.isLimit() && request.getSize() == 0) {
            log.info("Inside limit to back fill for request :{}",request);
            businessInstagramAccounts = businessInstagramAccountRepository.findByInstagramAccountIdIn(Collections.singletonList(request.getPageId()));
            BackfillRequest backfillRequest = new BackfillRequest();
            backfillRequest.setBackfillInsightReq(request);
            backfillRequest.setBusinessInstagramAccount(businessInstagramAccounts.get(0));
            kafkaProducerService.sendObjectV1(KafkaTopicEnum.BACKFILL_ENGAGEMENT_BREAKDOWN.getName(),backfillRequest);
        } else if(request.isLimit()) {
            businessInstagramAccounts = businessInstagramAccountRepository.findAllPagesByIsValid(new PageRequest(request.getPage(),request.getSize()));
            if(CollectionUtils.isEmpty(businessInstagramAccounts)) return;
            BackfillRequest backfillRequest = new BackfillRequest();
            backfillRequest.setBackfillInsightReq(request);
            for(BusinessInstagramAccount instagramAccount :  businessInstagramAccounts){
                if(!socialBusinessPropertyService.isSocialPostingAndReportingEnabled(instagramAccount.getEnterpriseId())){
                    log.info("Reporting is disabled for enterprise :{}",instagramAccount.getEnterpriseId());
                    continue;
                }
                log.info("Business Instagram pages sent for migration :{}",instagramAccount.getInstagramAccountId());
                backfillRequest.setBusinessInstagramAccount(instagramAccount);
                kafkaProducerService.sendObjectV1(KafkaTopicEnum.BACKFILL_ENGAGEMENT_BREAKDOWN.getName(),backfillRequest);
            }
        } else {
            int page = request.getPage();
            int size = request.getSize();
            while (true){
                log.info("Page :{} , size :{}",page,size);
                businessInstagramAccounts = businessInstagramAccountRepository.findAllPagesByIsValid(new PageRequest(page,size));
                if(CollectionUtils.isEmpty(businessInstagramAccounts)){
                    log.info("All pages are sent to nifi queue. No pages left for back fill");
                    break;
                }
                page += 10;
                BackfillRequest backfillRequest = new BackfillRequest();
                backfillRequest.setBackfillInsightReq(request);
                for(BusinessInstagramAccount instagramAccount :  businessInstagramAccounts){
                    if(!socialBusinessPropertyService.isSocialPostingAndReportingEnabled(instagramAccount.getEnterpriseId())){
                        log.info("Reporting is disabled for enterprise :{}",instagramAccount.getEnterpriseId());
                        continue;
                    }
                    log.info("Business Instagram pages sent for migration :{}",instagramAccount.getInstagramAccountId());
                    backfillRequest.setBusinessInstagramAccount(instagramAccount);
                    kafkaProducerService.sendObjectV1(KafkaTopicEnum.BACKFILL_ENGAGEMENT_BREAKDOWN.getName(),backfillRequest);
                }
            }
        }
    }

    @Override
    public LeadershipByPostsDataPoints getPostLeadershipReport(LocationReportRequest insightsRequest,
                                                               ReportSortingCriteria postSortingCriteria, SortOrder order,
                                                               Integer startIndex, Integer pageSize) {
        List<BusinessInstagramAccountRepository.BIA> instagramPages =
                businessInstagramAccountRepository.findByBusinessIds(insightsRequest.getBusinessIds());
        if(CollectionUtils.isEmpty(instagramPages)) return new LeadershipByPostsDataPoints();
        Map<String, BusinessInstagramAccountRepository.BIA> pageMap = getPageIdBFPMap(instagramPages);
        SearchResponse searchResponseForPage =
                reportsEsService.getDataFromESPageIndex(insightsRequest,postSortingCriteria,order,
                        startIndex,pageSize,new ArrayList<>(pageMap.keySet()),ElasticConstants.INSTAGRAM_PAGE_INSIGHTS.getName());
        LinkedHashMap<String,LeadershipByPostsResponse> leadershipByPostsResponseMap =
                reportDataConverter.convertSearchResponseToLeadership(searchResponseForPage);
        SearchResponse searchResponseForPost =
                reportsEsService.getDataFromESPostIndex(insightsRequest,postSortingCriteria,order,startIndex,pageSize,
                        new ArrayList<>(leadershipByPostsResponseMap.keySet()),SocialChannel.INSTAGRAM.getId());
        SearchResponse searchResponseForFollowers = reportsEsService.getDataFromESFollowers(insightsRequest,
                postSortingCriteria,order,startIndex,pageSize, new ArrayList<>(leadershipByPostsResponseMap.keySet()),
                ElasticConstants.INSTAGRAM_PAGE_INSIGHTS.getName());
        reportDataConverter.updateResponseWithPostCount(searchResponseForPost,searchResponseForFollowers,leadershipByPostsResponseMap);
        LeadershipByPostsDataPoints postsDataPoints = new LeadershipByPostsDataPoints();
        setLocationData(leadershipByPostsResponseMap, instagramPages);
        postsDataPoints.setDataPoints(new ArrayList<>(leadershipByPostsResponseMap.values()));
        postsDataPoints.setTotalRecords(pageMap.size());
        return postsDataPoints;
    }

    private Map<Integer, String> getBusinessIdAndPageIdMap(List<String> pageIds,
                                                           List<BusinessInstagramAccountRepository.BIA> pages) {
        return pages.stream().filter(p -> pageIds.contains(p.getInstagramAccountId())).
                collect(Collectors.toMap(BusinessInstagramAccountRepository.BIA::getBusinessId,
                        BusinessInstagramAccountRepository.BIA::getInstagramAccountId));
    }

    @NotNull
    private Map<String, BusinessInstagramAccountRepository.BIA> getPageIdBFPMap(List<BusinessInstagramAccountRepository.BIA> pages) {
        return pages.stream()
                .collect(Collectors.toMap(BusinessInstagramAccountRepository.BIA::getInstagramAccountId, page -> page));
    }

    private void setLocationData(Map<String, LeadershipByPostsResponse> leadershipByPostsResponseMap,
                                 List<BusinessInstagramAccountRepository.BIA> pages) {
        Map<Integer,String> mapOfBusinessIdAndPageId =
                getBusinessIdAndPageIdMap(new ArrayList<>(leadershipByPostsResponseMap.keySet()), pages);
        List<BusinessBizLiteDto> listOfBusinessLite =
                coreService.getBusinessLiteDtoByBusinessIds(new ArrayList<>(mapOfBusinessIdAndPageId.keySet()));
        listOfBusinessLite.forEach(lite -> {
            String pageId = mapOfBusinessIdAndPageId.get(lite.getId());
            LeadershipByPostsResponse response = leadershipByPostsResponseMap.get(pageId);
            response.setLabel(StringUtils.isEmpty(lite.getAlias1()) ? lite.getName() : lite.getAlias1());
            response.setLocation(lite.getBusinessNumber());
        });
    }

    private PostData createPostDataForCDN(BusinessPosts businessPosts) {
        PostData postData = new PostData();
        List<String> media;
        postData.setId(businessPosts.getId());
        postData.setBePostId(businessPosts.getBePostId()!=null?businessPosts.getBePostId().toString()
                :null);
        postData.setPostId(businessPosts.getPostId());
        postData.setSourceId(businessPosts.getSourceId());
        postData.setPageName(businessPosts.getPageName());
        postData.setPageId(businessPosts.getExternalPageId());
        postData.setBusinessId(businessPosts.getBusinessId());
        postData.setEnterpriseId(businessPosts.getEnterpriseId());
        postData.setPostUrl(businessPosts.getPostUrl());
        postData.setPostText(businessPosts.getPostText());
        postData.setPostingDate(businessPosts.getPublishDate());
        postData.setPublisherId(businessPosts.getCreatedByBeUserId());
        postData.setPublisherEmail(businessPosts.getCreatedByBeUserEmail());
        postData.setPublisherName(businessPosts.getCreatedByBeUserName());
        if(StringUtils.isNotEmpty(businessPosts.getImageUrls())) {
            media = Arrays.asList(businessPosts.getImageUrls().split(","));
            postData.setImages(media);
        }
        if( StringUtils.isNotEmpty(businessPosts.getVideoUrls())) {
            media = Arrays.asList(businessPosts.getVideoUrls().split(","));
            postData.setVideos(media);
        }
        postData.setPostType(businessPosts.getPostType());

        try {
            ObjectMapper objectMapper = new ObjectMapper();
            String insightData = businessPosts.getResponse();
            PostInsightDTO insightDTO = objectMapper.readValue(insightData, PostInsightDTO.class);
            postData.setEngagement(insightDTO.getEngagement());
            postData.setImpression(insightDTO.getImpression());
            postData.setReach(insightDTO.getReach());
            postData.setLikeCount(insightDTO.getLikeCount());
            postData.setCommentCount(insightDTO.getCommentCount());
            postData.setShareCount(insightDTO.getShareCount());
            postData.setClickCount(insightDTO.getClickCount());
            postData.setVideoViews(insightDTO.getVideoViews());
            if(Objects.nonNull(postData.getEngagement()) && Objects.nonNull(postData.getReach()) && postData.getReach()!=0) {
                postData.setEngagementRate((double)postData.getEngagement()/postData.getReach());
            }
        } catch (Exception e) {
            log.info("Exception while converting response to PostInsightDTO: ",e);
        }
        return postData;
    }

    private void auditPostInsight(BusinessPosts businessPosts, boolean isFailed) {
        PostInsightAudit postInsightAudit = new PostInsightAudit();
        postInsightAudit.setPostId(businessPosts.getPostId());
        postInsightAudit.setSourceId(businessPosts.getSourceId());
        postInsightAudit.setPageId(businessPosts.getExternalPageId());
        if(isFailed) {
            postInsightAudit.setResponse(businessPosts.getError());
        } else {
            postInsightAudit.setResponse(businessPosts.getResponse());
        }
        postInsightAuditRepo.saveAndFlush(postInsightAudit);
    }

    private PostInsightDTO convertIgDataToPostInsightData(List<IgData> igDataList, InstagramAccountDataResponse engagementResponse, boolean isReel) {
        PostInsightDTO postInsightDTO = new PostInsightDTO();
        for(IgData igData: igDataList) {
            String name = igData.getName();
            switch (name) {
                case InsightsConstants.REACH:
                    postInsightDTO.setReach((Integer) igData.getValues().get(0).getValue());
                    break;
                case InsightsConstants.VIEWS:
                    if(isReel) {
                        postInsightDTO.setVideoViews((Integer) igData.getValues().get(0).getValue());
                    } else {
                        postInsightDTO.setImpression((Integer) igData.getValues().get(0).getValue());
                    }
                    break;
                case InsightsConstants.IG_TOTAL_INTERACTION:
                case InsightsConstants.ENGAGEMENT:
                    postInsightDTO.setEngagement((Integer) igData.getValues().get(0).getValue());
                    break;
            }
        }
        if(Objects.nonNull(engagementResponse)) {
            postInsightDTO.setLikeCount(engagementResponse.getLikeCount());
            postInsightDTO.setCommentCount(engagementResponse.getCommentCount());
            postInsightDTO.setShareCount(0);
        }

        return postInsightDTO;
    }

    private List<String> getPageIdsByBids(List<Integer> businessIds){
        List<String> pageIds =  businessInstagramAccountRepository.findDistinctInstagramIdByBusinessIdIn(businessIds);
        if(CollectionUtils.isEmpty(pageIds)){
            return new ArrayList<>();
        }
        return pageIds;
    }

    @Override
    public void saveCDNPostToES(BusinessPosts businessPosts) {
        PostData postData = createPostDataForCDN(businessPosts);
        sendPostDataToKafka(postData);
    }

    @Override
    public PerformanceSummaryResponse getInstagramPerformanceData(InsightsRequest insights) {
        try {
            log.info("Request to get insight for pages from business ids with enterprise id: {}", insights.getEnterpriseId());
            String index = ElasticConstants.INSTAGRAM_PAGE_INSIGHTS.getName();
            List<String> pageIds = getPageIdsByBids(insights.getBusinessIds());
            if(CollectionUtils.isEmpty(pageIds)) {
                log.info("No valid pageId found for businessIds {}", insights.getBusinessIds());
                return null;
            }
            InsightsESRequest request = reportDataConverter.createESRequestForPerformanceData(insights,index,pageIds, SocialChannel.INSTAGRAM.getId());
            return reportsEsService.getPagePerformanceDataFromEsChannelWise(request);

        } catch (Exception ex) {
            log.error("Something went wrong while parsing data for performance summary for request {}", insights, ex);
            return null;
        }
    }

    @Override
    public boolean backfillProfilePagesToEs(PageInsightsRequest pageInsights) {
        List<BusinessInstagramAccount> pages = businessInstagramAccountRepository.findByInstagramAccountIdInAndEligible(pageInsights.getPageIds());
        log.info("Page found for backfillProfilePagesToEs {}",pages);
        if(CollectionUtils.isNotEmpty(pages)) {
            List<BackfillInsightReq> eligibleEvents = reportDataConverter.conversionToIGScanEventDTO(pages, pageInsights);
            eligibleEvents.parallelStream().forEach(this::sendSocialRecordScannedEvent);
            return true;
        }
        return false;
    }
    private void sendSocialRecordScannedEvent(BackfillInsightReq payload) {
        kafkaProducerService.sendObjectV1(SOCIAL_BACKFILL_PAGE_SCAN_EVENT, payload);
    }
    public PageReportEsData getInstagramInsightsReportData(InsightsRequest insights) throws Exception {
        log.info("Request to get insight for pages from business ids with enterprise id: {}", insights.getEnterpriseId());
        String index = ElasticConstants.INSTAGRAM_PAGE_INSIGHTS.getName();
        List<String> pageIds = getPageIdsByBids(insights.getBusinessIds());
        if(CollectionUtils.isEmpty(pageIds)){
            return new PageReportEsData();
        }

        long diff = insights.getEndDate().getTime() - insights.getStartDate().getTime();
        long noOfDaysBetween = TimeUnit.DAYS.convert(diff, TimeUnit.MILLISECONDS);
        reportDataConverter.setGroupByType(insights,noOfDaysBetween);
        InsightsESRequest request = reportDataConverter.createESRequestForPage(insights,index,pageIds, SocialChannel.INSTAGRAM.getId());
        PageReportEsData pageInsightDataFromEs = reportsEsService.getPageReportDataFromEsChannelWise(request);

        pageInsightDataFromEs.setDateDiff(reportsEsService.getTimeDifference(request));
        log.info("Fetched insight from es for pages");

        return pageInsightDataFromEs;
    }

    @Override
    public PageReportEsData getMessageVolumeInsightsReportData(InsightsRequest insights) throws Exception {
        log.info("Request to get insight for pages from business ids with enterprise id: {}", insights.getEnterpriseId());
        PageReportEsData reportEsData = new PageReportEsData();
        List<String> pageIds = getPageIdsByBids(insights.getBusinessIds());
        if(CollectionUtils.isEmpty(pageIds)){
            return reportEsData;
        }

        long diff = insights.getEndDate().getTime() - insights.getStartDate().getTime();
        long noOfDaysBetween = TimeUnit.DAYS.convert(diff, TimeUnit.MILLISECONDS);
        reportDataConverter.setGroupByType(insights,noOfDaysBetween);

        List<String> sentFeedType = Arrays.asList(EngageV2FeedTypeEnum.POST.name(),EngageV2FeedTypeEnum.AD_POST.name(),
                EngageV2FeedTypeEnum.COMMENT.name(), EngageV2FeedTypeEnum.AD_COMMENT.name());
        List<String> receiveFeedType = Arrays.asList(EngageV2FeedTypeEnum.POST.name(), EngageV2FeedTypeEnum.COMMENT.name());

        InsightsESRequest sentMessageRequest = reportDataConverter.createESRequestForMessage(insights, ElasticConstants.SOCIAL_ENGAGE_V2_FEED.getName(),
                pageIds, SocialChannel.INSTAGRAM.getId(), sentFeedType, null, receiveFeedType, null);

        sentMessageRequest.setSearchTemplate(SearchTemplate.REPORT_SENT_MESSAGE_VOLUME);
        PageReportEsData sentMessageEsData = reportsEsService.getPageReportDataFromEsChannelWise(sentMessageRequest);
        reportEsData.setSentMessage(sentMessageEsData.getSentMessage());

        sentMessageRequest.setSearchTemplate(SearchTemplate.REPORT_RECEIVED_MESSAGE_VOLUME);
        PageReportEsData receivedMessageEsData = reportsEsService.getPageReportDataFromEsChannelWise(sentMessageRequest);
        reportEsData.setReceivedMessage(receivedMessageEsData.getReceivedMessage());
        log.info("Fetched insight from es for pages");
        return reportEsData;
    }

    @Override
    public PostDataAndInsightResponse getInstagramInsightsForTopPost(InsightsRequest insights, int startIndex, int pageSize, String sortParam, String sortOrder, boolean excelDownload) {
        log.info("Request to get post insight with enterprise id: {} for instagram", insights.getEnterpriseId());
        List<List<Integer>> batches = TopPostsReportUtils.splitListIntoBatches(insights.getBusinessIds(), BATCH_SIZE_FOR_TOP_POSTS);
        ExecutorService executorService = Executors.newFixedThreadPool(THREADPOOL_SIZE_FOR_TOP_POSTS);
        List<Future<PostDataAndInsightResponse>> futures = new ArrayList<>();
        for (List<Integer> batch : batches) {
            List<String> pageIds = getPageIdsByBids(batch);
            InsightsPostRequest insightsPostRequest = new InsightsPostRequest(insights, startIndex, pageSize, sortParam, sortOrder, Integer.toString(SocialChannel.INSTAGRAM.getId()), pageIds);
            futures.add(executorService.submit(() -> {
                InsightsESRequest request = reportDataConverter.createESRequestForTopPosts(insightsPostRequest, ElasticConstants.POST_INSIGHTS.getName());
                return reportsEsService.getPostDataFromEs(request, excelDownload);
            }));
        }
        List<PostDataAndInsightResponse> combinedResponses = new ArrayList<>();
        try {
            for (Future<PostDataAndInsightResponse> future : futures) {
                combinedResponses.add(future.get());
            }
        } catch (InterruptedException | ExecutionException e) {
            log.error("Async Exception occurred in [getInstagramInsightsForTopPost] for enterpriseId : {}, exception :  {}", insights.getEnterpriseId(), e.getMessage());
        } finally {
            executorService.shutdown();
        }
        return TopPostsReportUtils.getSortedInsightResponse(combinedResponses, pageSize, sortParam, sortOrder);
    }

}