package com.birdeye.social.service;

import com.birdeye.social.entities.SocialPost;
import com.birdeye.social.entities.SocialPostsAssets;
import com.birdeye.social.model.MediaData;
import com.birdeye.social.model.PostAssetsData;

import java.util.List;
import java.util.Map;

public interface PostAssetService {
    List<MediaData> getMediaDataForCal(String imageIds, Long enterpriseLongId, String type);

    List<String> getMediaRequestForCal(String imageIds, Long enterpriseBizNum, String type);

    PostAssetsData setSocialPostsAssetsDataForCal(String assetIds, Long parentId, String type);

    List<SocialPostsAssets> getPostsAssetsById(Map<Integer, SocialPostsAssets> postAssetsMap, String imageIds);

    List<SocialPostsAssets> getPostsAssetsById(Map<Integer, SocialPostsAssets> postAssetsMap, List<Integer> imageIds);

    Map<Integer, SocialPostsAssets> getPostAssetsForList(List<SocialPost> socialMasterPostList);

    List<MediaData> getMediaDataV2(List<SocialPostsAssets> postAssets, Long enterpriseLongId, String type);

    List<String> getMediaRequestV2(List<SocialPostsAssets> postsAssets, Long enterpriseBizNum, String type);

    PostAssetsData setSocialPostsAssetsDataV2(List<SocialPostsAssets> socialPostAssets, Long parentId, String type);
}
