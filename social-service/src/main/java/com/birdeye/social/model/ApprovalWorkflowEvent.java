package com.birdeye.social.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class ApprovalWorkflowEvent {
    private Integer entityId;
    private String entityName;
    private Integer approvalWorkflowId;
    private Integer approvalRequestId;
    private String approvalUUId;
    private String status;
    List<ApprovalSteps> steps;
    private Integer submitterId;
    private String remarks;
    private Integer referenceStepId;
    CurrentApprovalDetails currentActionDetails;
    private String eventType;
    private Long updatedAt;
    private String updatedBy;
    private List<String> pendingApproverIds;
}
